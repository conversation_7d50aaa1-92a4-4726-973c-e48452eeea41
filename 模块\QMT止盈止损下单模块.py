#coding:gbk
"""
QMT止盈止损和下单模块 - 标准框架版本
从6sk线.py中提取的核心交易模块，适用于QMT实盘环境

主要功能：
1. 移动止盈止损逻辑
2. 买入/卖出订单执行 (支持正负偏移调整)
3. 委托查询和撤单功能
4. 风险控制机制

挂单偏移策略：
- buy_hang_offset_ratio: 买入挂单偏移比例
  * 正值(如0.0002): 低于市价买入，保守策略，提高成交概率
  * 负值(如-0.0002): 高于市价买入，激进策略，可能错过最佳价格
- sell_hang_offset_ratio: 卖出挂单偏移比例
  * 正值(如0.0002): 高于市价卖出，保守策略，提高成交概率
  * 负值(如-0.0002): 低于市价卖出，激进策略，快速成交但可能损失收益

使用方法：
在QMT中导入此模块，调用相应函数
"""

import numpy as np
import pandas as pd
import datetime
import time
from typing import Dict, List, Tuple, Optional
from collections import deque

# ============================================================================
# 数据缓冲区管理类
# ============================================================================

class MarketDataBuffer:
    """
    市场数据缓冲区管理类 - 实现方案三的核心功能
    支持历史数据预加载和实时数据增量更新
    """

    def __init__(self, buffer_size=100):
        """
        初始化数据缓冲区

        Args:
            buffer_size: 缓冲区大小，默认100根K线
        """
        self.buffer_size = buffer_size

        # 价格数据缓冲区（存储合成后的K线数据）
        self.close_buffer = deque(maxlen=buffer_size)
        self.high_buffer = deque(maxlen=buffer_size)
        self.low_buffer = deque(maxlen=buffer_size)
        self.open_buffer = deque(maxlen=buffer_size)

        # 成交量数据缓冲区
        self.volume_buffer = deque(maxlen=buffer_size)

        # 时间戳缓冲区
        self.time_buffer = deque(maxlen=buffer_size)

        # Tick数据临时缓冲区（用于合成K线）
        self.tick_buffer = deque(maxlen=10)  # 临时存储tick数据
        self.pending_ticks = []  # 等待合成的tick数据

        # 指标缓存
        self.indicators = {}

        # 初始化状态
        self.is_initialized = False

        print(f"📊 数据缓冲区初始化完成，容量: {buffer_size}根K线（基于tick合成）")

    def preload_history_data(self, ContextInfo, stock_code, period='tick', count=100):
        """
        预加载历史数据填充缓冲区 - 专门针对tick数据优化

        Args:
            ContextInfo: QMT上下文对象
            stock_code: 股票代码
            period: 数据周期（默认tick）
            count: 获取数量
        """
        try:
            print(f"📥 开始预加载历史数据: {stock_code}, 周期: {period}, 数量: {count}")

            # 针对tick数据的特殊处理
            if period == 'tick':
                print(f"  📊 Tick数据模式 - 直接获取最近{count}个tick数据")

                # 直接使用count获取指定数量的tick数据（避免获取过多数据）
                hist_data = ContextInfo.get_market_data_ex(
                    [],  # 获取所有字段（QMT会自动处理tick支持的字段）
                    [stock_code],
                    period='tick',
                    count=count,  # 只获取需要的数量
                    dividend_type='none'  # tick数据不需要复权
                )
            else:
                # 非tick数据的处理（保持原有逻辑）
                hist_data = ContextInfo.get_market_data_ex(
                    [],  # 获取所有字段
                    [stock_code],
                    period=period,
                    count=count,
                    dividend_type='back_ratio'  # 后复权
                )

            if hist_data is None or stock_code not in hist_data:
                print(f"⚠️ 历史数据获取失败，尝试备用方案...")
                # 备用方案：获取最近数据
                backup_fields = [] if period == 'tick' else []
                hist_data = ContextInfo.get_market_data_ex(
                    backup_fields,
                    [stock_code],
                    period=period,
                    count=min(count, 50),  # 减少数量
                    dividend_type='none'
                )

            if hist_data is None or stock_code not in hist_data:
                print(f"❌ 历史数据获取失败，将使用实时数据逐步填充缓冲区")
                return False

            # 解析历史数据
            data_df = hist_data[stock_code]
            data_count = len(data_df)

            if period == 'tick':
                print(f"✅ 成功获取 {data_count} 个历史tick数据")
            else:
                print(f"✅ 成功获取 {data_count} 根历史K线数据")

            # 填充缓冲区 - 针对tick数据特殊处理
            if period == 'tick':
                print(f"📊 处理历史tick数据，将合成K线...")
                # 调试：打印数据结构信息
                print(f"📊 数据列名: {list(data_df.columns) if hasattr(data_df, 'columns') else 'N/A'}")
                if data_count > 0:
                    first_row = data_df.iloc[0]
                    print(f"📊 第一行数据字段: {list(first_row.keys()) if hasattr(first_row, 'keys') else 'N/A'}")

                # Tick数据需要合成K线
                tick_data_list = []

                for i in range(data_count):
                    row = data_df.iloc[i]

                    # 构造tick数据 - 尝试多种可能的字段名
                    # QMT tick数据的字段名可能有变化，尝试多种可能性
                    tick_price = 0
                    for price_field in ['lastPrice', 'last_price', 'price', 'close', 'last']:
                        if price_field in row and row[price_field] is not None:
                            tick_price = float(row[price_field])
                            break

                    tick_volume = 0
                    for volume_field in ['volume', 'lastVolume', 'last_volume', 'vol']:
                        if volume_field in row and row[volume_field] is not None:
                            tick_volume = float(row[volume_field])
                            break

                    time_val = row.get('time', row.get('timetag', ''))

                    tick_data_list.append({
                        'price': tick_price,
                        'volume': tick_volume,
                        'time': time_val
                    })

                # 将tick数据两两合成K线
                kline_count = 0
                for i in range(0, len(tick_data_list) - 1, 2):
                    if i + 1 < len(tick_data_list):
                        # 合成K线
                        tick_pair = tick_data_list[i:i+2]
                        kline_data = self._merge_ticks_to_kline(tick_pair)

                        # 添加到缓冲区
                        self._add_kline_to_buffer(kline_data)
                        kline_count += 1

                print(f"✅ 历史tick数据合成完成：{data_count}个tick → {kline_count}根K线")

            else:
                # K线数据处理（原有逻辑）
                for i in range(data_count):
                    row = data_df.iloc[i]

                    self.close_buffer.append(float(row.get('close', row.get('lastPrice', 0))))
                    self.high_buffer.append(float(row.get('high', row.get('lastPrice', 0))))
                    self.low_buffer.append(float(row.get('low', row.get('lastPrice', 0))))
                    self.open_buffer.append(float(row.get('open', row.get('lastPrice', 0))))
                    self.volume_buffer.append(float(row.get('volume', 0)))

                    # 时间戳处理
                    time_val = row.get('time', row.get('timetag', ''))
                    self.time_buffer.append(str(time_val))

            self.is_initialized = True
            print(f"📊 缓冲区预加载完成，当前数据量: {len(self.close_buffer)}根K线")
            return True

        except Exception as e:
            print(f"❌ 历史数据预加载异常: {e}")
            import traceback
            traceback.print_exc()
            return False

    def update_realtime_data(self, close_price, high_price=None, low_price=None,
                           open_price=None, volume=0, timestamp=None):
        """
        更新实时数据到缓冲区

        对于tick数据，推荐使用add_tick_data方法来自动合成K线
        此方法保留用于直接添加K线数据的兼容性

        Args:
            close_price: 收盘价/当前价
            high_price: 最高价
            low_price: 最低价
            open_price: 开盘价
            volume: 成交量
            timestamp: 时间戳
        """
        # 添加新数据到缓冲区（自动维护大小）
        self.close_buffer.append(float(close_price))
        self.high_buffer.append(float(high_price or close_price))
        self.low_buffer.append(float(low_price or close_price))
        self.open_buffer.append(float(open_price or close_price))
        self.volume_buffer.append(float(volume))
        self.time_buffer.append(str(timestamp or datetime.datetime.now()))

        # 清除过期的指标缓存
        self.indicators.clear()

    def update_tick_data(self, tick_price, tick_volume, timestamp=None):
        """
        更新tick数据（推荐用于tick策略）
        自动将tick数据合成K线后添加到缓冲区

        Args:
            tick_price: tick价格
            tick_volume: tick成交量
            timestamp: 时间戳
        """
        self.add_tick_data(tick_price, tick_volume, timestamp)

    def calculate_ma(self, period):
        """计算移动平均线"""
        if len(self.close_buffer) < period:
            return None

        cache_key = f'ma_{period}'
        if cache_key not in self.indicators:
            recent_prices = list(self.close_buffer)[-period:]
            self.indicators[cache_key] = sum(recent_prices) / period

        return self.indicators[cache_key]

    def add_tick_data(self, tick_price, tick_volume, timestamp=None):
        """
        添加tick数据并尝试合成K线

        Args:
            tick_price: tick价格
            tick_volume: tick成交量
            timestamp: 时间戳
        """
        try:
            # 构造tick数据
            tick_data = {
                'price': tick_price,
                'volume': tick_volume,
                'time': timestamp or datetime.datetime.now()
            }

            # 添加到tick缓冲区
            self.pending_ticks.append(tick_data)

            # 检查是否可以合成K线（QMT tick是3秒一个，两个tick合成一个K线）
            if len(self.pending_ticks) >= 2:
                # 合成K线
                kline_data = self._merge_ticks_to_kline(self.pending_ticks[:2])

                # 将合成的K线添加到缓冲区
                self._add_kline_to_buffer(kline_data)

                # 移除已合成的tick
                self.pending_ticks = self.pending_ticks[2:]

                # 清除指标缓存
                self.indicators.clear()

                # 显示详细的成交量计算信息
                original_vols = kline_data.get('original_volumes', [])
                if len(original_vols) >= 2:
                    print(f"📊 Tick合成K线: OHLC[{kline_data['open']:.3f}, {kline_data['high']:.3f}, {kline_data['low']:.3f}, {kline_data['close']:.3f}]")
                    print(f"   📈 成交量计算: {original_vols[1]:.0f} - {original_vols[0]:.0f} = {kline_data['volume']:.0f} (增量)")
                else:
                    print(f"📊 Tick合成K线: OHLC[{kline_data['open']:.3f}, {kline_data['high']:.3f}, {kline_data['low']:.3f}, {kline_data['close']:.3f}], 成交量={kline_data['volume']:.0f}")

        except Exception as e:
            print(f"❌ Tick数据处理异常: {e}")

    def _merge_ticks_to_kline(self, tick_list):
        """
        将tick数据合成为K线

        Args:
            tick_list: tick数据列表

        Returns:
            dict: 合成的K线数据
        """
        if len(tick_list) < 2:
            raise ValueError("至少需要2个tick数据来合成K线")

        # 计算OHLC
        prices = [tick['price'] for tick in tick_list]
        open_price = tick_list[0]['price']
        close_price = tick_list[-1]['price']
        high_price = max(prices)
        low_price = min(prices)

        # 计算增量成交量（tick数据是累积成交量，需要计算差值）
        # 第一个tick的增量 = 当前累积 - 上一个K线的累积（如果有的话）
        # 第二个tick的增量 = 第二个tick累积 - 第一个tick累积
        if len(tick_list) == 2:
            # 两个tick的情况：第二个tick的累积成交量 - 第一个tick的累积成交量
            volume_increment = tick_list[1]['volume'] - tick_list[0]['volume']
            # 如果增量为负或为0，使用最小增量
            if volume_increment <= 0:
                volume_increment = max(1, tick_list[1]['volume'] / 1000)  # 保守估算
        else:
            # 多个tick的情况：最后一个 - 第一个
            volume_increment = tick_list[-1]['volume'] - tick_list[0]['volume']
            if volume_increment <= 0:
                volume_increment = max(1, tick_list[-1]['volume'] / 1000)

        return {
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume_increment,  # 使用增量成交量
            'start_time': tick_list[0]['time'],
            'end_time': tick_list[-1]['time'],
            'original_volumes': [tick['volume'] for tick in tick_list]  # 保留原始累积成交量用于调试
        }

    def _add_kline_to_buffer(self, kline_data):
        """
        将K线数据添加到缓冲区

        Args:
            kline_data: K线数据字典
        """
        self.open_buffer.append(kline_data['open'])
        self.high_buffer.append(kline_data['high'])
        self.low_buffer.append(kline_data['low'])
        self.close_buffer.append(kline_data['close'])
        self.volume_buffer.append(kline_data['volume'])
        self.time_buffer.append(str(kline_data.get('end_time', '')))

    def calculate_atr(self, period=14):
        """计算ATR（平均真实波幅）"""
        if len(self.close_buffer) < period + 1:
            return None

        cache_key = f'atr_{period}'
        if cache_key not in self.indicators:
            highs = list(self.high_buffer)[-period-1:]
            lows = list(self.low_buffer)[-period-1:]
            closes = list(self.close_buffer)[-period-1:]

            true_ranges = []
            for i in range(1, len(highs)):
                tr1 = highs[i] - lows[i]
                tr2 = abs(highs[i] - closes[i-1])
                tr3 = abs(lows[i] - closes[i-1])
                true_ranges.append(max(tr1, tr2, tr3))

            if true_ranges:
                self.indicators[cache_key] = sum(true_ranges) / len(true_ranges)
            else:
                self.indicators[cache_key] = 0

        return self.indicators[cache_key]

    def get_current_price(self):
        """获取当前价格"""
        return self.close_buffer[-1] if self.close_buffer else None

    def get_current_volume(self):
        """获取当前成交量"""
        return self.volume_buffer[-1] if self.volume_buffer else 0

    def get_data_count(self):
        """获取缓冲区数据量"""
        return len(self.close_buffer)

    def is_ready_for_trading(self, min_periods=20):
        """检查是否有足够数据进行交易"""
        return len(self.close_buffer) >= min_periods

# ============================================================================
# 全局变量和配置
# ============================================================================

# 策略配置参数
STRATEGY_CONFIG = {
    'max_position_ratio': 0.7,      # 最大仓位比例
    'min_cash_reserve': 100,      # 最小资金保留
    'buy_hang_offset_ratio': 0.002, # 买入挂单偏移比例 (正值=低于市价买入, 负值=高于市价买入)
    'sell_hang_offset_ratio': 0.002,# 卖出挂单偏移比例 (正值=高于市价卖出, 负值=低于市价卖出)
    'fixed_stop_loss': 0.5,         # 固定止损百分比
    'use_trailing_stop': True,      # 是否使用移动止盈
    'use_main_stock': True,         # 是否使用主图标的
    'donchian_period': 20,          # 唐奇安通道周期
    'atr_period': 14,               # ATR计算周期
    'atr_stop_loss_multiplier': 3.0, # ATR止损倍数
    'atr_trigger_multiplier': 4.0,  # ATR移动止盈触发倍数
    'min_move_threshold': 0.1,      # 最小移动幅度
    # K线合成配置
    'enable_kline_merge': True,     # 是否启用K线合成
    'merge_ratio': 2,               # 合成比例 (2根合成1根)
    # 数据缓冲区配置（针对tick数据优化）
    'enable_data_buffer': True,     # 是否启用数据缓冲区功能
    'buffer_size': 100,             # tick数据缓冲区大小（只需要计算指标的数据）
    'history_data_count': 60,       # 预加载历史tick数据数量（只需要30根K线=60个tick）
    'min_trading_periods': 20,      # 开始交易所需的最少tick数据周期（10根K线=20个tick）
    'debug_mode': False,            # 调试模式
    # 委托状态配置
    'pending_status_codes': [48, 49, 50, 51],  # 被认为是"未处理"的状态代码（不包括-1）
    'debug_status_mapping': True,   # 是否显示状态映射调试信息
    'status_minus_one_as_pending': False,  # 是否将Status=-1视为未处理（默认False，避免误判）

    # 委托查询过滤配置
    'filter_cancelled_orders': True,    # 是否过滤已撤销的委托（提高效率）
    'filter_completed_orders': True,    # 是否过滤已完成的委托（提高效率）
    'max_buffer_size': 10,          # 最大缓冲区大小
    'max_history_bars': 200,        # 最大历史K线数量
    'convert_cumulative_volume': True,  # 是否将累积成交量转换为增量成交量
    # 动态触发阈值配置
    'use_dynamic_trigger': True,    # 是否使用动态触发阈值
    'volatility_period': 20,        # 波动率计算周期
    'low_volatility_trigger': 3.0,  # 低波动率时的触发阈值
    'high_volatility_trigger': 8.0, # 高波动率时的触发阈值

    # 买入信号配置
    'buy_signal_count_required': 3, # 需要连续接收的数据次数
    'buy_signal_enabled': True,     # 是否启用买入信号检查
}

# ============================================================================
# 策略初始化和数据管理函数 - 方案三实现
# ============================================================================

def init_strategy_download_only(ContextInfo):
    """
    策略初始化函数 - 仅下载数据（QMT标准模式）
    注意：在init()中只能下载数据，不能获取数据
    """
    print("🚀 QMT止盈止损策略初始化 - 数据缓冲区版本")
    print("="*60)

    # 获取股票代码
    stock_code = ContextInfo.stockcode + '.' + ContextInfo.market
    print(f"📊 目标股票: {stock_code}")
    print(f"🕐 初始化时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 下载历史数据（QMT要求在init中完成）
    print(f"\n📥 开始下载历史数据...")

    try:
        # 计算下载日期范围
        end_date = datetime.datetime.now().strftime('%Y%m%d')
        start_date = (datetime.datetime.now() - datetime.timedelta(days=10)).strftime('%Y%m%d')

        print(f"📅 下载范围: {start_date} ~ {end_date}")

        # 下载分钟级历史数据
        download_history_data(stock_code, "1m", start_date, end_date)
        print(f"✅ 分钟数据下载完成")

        # 下载日线数据用于长期指标
        download_history_data(stock_code, "1d", start_date, end_date)
        print(f"✅ 日线数据下载完成")

    except Exception as e:
        print(f"⚠️ 历史数据下载异常: {e}")
        print(f"💡 提示: 将在handlebar中逐步填充数据缓冲区")

    # 等待数据写入完成
    print(f"\n⏳ 等待数据写入完成...")
    time.sleep(5)

    # 标记初始化完成，但数据缓冲区将在第一次handlebar中创建
    ContextInfo.init_completed = True
    ContextInfo.buffer_initialized = False

    print(f"\n💡 策略初始化完成，等待handlebar进行数据获取...")
    print("="*60)

# 注意：initialize_buffer_in_handlebar函数已移除
# 所有数据缓冲区初始化现在都在init()函数中完成

# 注意：handle_bar_with_buffer_qmt函数已移除
# 所有逻辑现在都在主handlebar()函数中，历史数据初始化在init()中完成

def update_buffer_with_current_data_qmt(ContextInfo):
    """
    使用QMT当前数据更新缓冲区 - 支持tick和K线数据
    """
    try:
        # 确保缓冲区存在
        if not hasattr(ContextInfo, 'data_buffer'):
            print("⚠️ 数据缓冲区不存在，跳过更新")
            return

        # 检查是否为tick模式
        period = getattr(ContextInfo, 'period', 'tick')

        if period == 'tick':
            # Tick数据处理 - 使用测试验证成功的方法3
            try:
                stock_code = ContextInfo.stockcode + '.' + ContextInfo.market

                # 使用get_market_data_ex + count=1获取最新tick数据（测试验证成功的方法）
                realtime_data = ContextInfo.get_market_data_ex(
                    [],  # 获取所有字段
                    [stock_code],
                    period='tick',
                    count=1,
                    dividend_type='none'
                )

                if realtime_data and stock_code in realtime_data:
                    data_df = realtime_data[stock_code]
                    if len(data_df) > 0:
                        latest_data = data_df.iloc[-1]

                        # 尝试多种可能的字段名（与历史数据处理保持一致）
                        tick_price = 0
                        for price_field in ['lastPrice', 'last_price', 'price', 'close', 'last']:
                            if price_field in latest_data and latest_data[price_field] is not None:
                                tick_price = float(latest_data[price_field])
                                break

                        tick_volume = 0
                        for volume_field in ['volume', 'lastVolume', 'last_volume', 'vol']:
                            if volume_field in latest_data and latest_data[volume_field] is not None:
                                tick_volume = float(latest_data[volume_field])
                                break

                        if tick_price > 0:
                            # 使用tick数据更新方法
                            ContextInfo.data_buffer.update_tick_data(tick_price, tick_volume)
                        else:
                            print(f"⚠️ 无法获取有效的tick价格数据，可用字段: {list(latest_data.keys())}")
                            return
                    else:
                        print("⚠️ 实时tick数据为空")
                        return
                else:
                    print("⚠️ 无法获取实时tick数据")
                    return
            except Exception as e:
                print(f"⚠️ 获取实时tick数据失败: {e}")
                return
        else:
            # K线数据处理（原有逻辑）
            try:
                # 方法1：直接从ContextInfo获取
                if hasattr(ContextInfo, 'close') and len(ContextInfo.close) > 0:
                    current_price = ContextInfo.close[-1]
                    high_price = ContextInfo.high[-1] if hasattr(ContextInfo, 'high') else current_price
                    low_price = ContextInfo.low[-1] if hasattr(ContextInfo, 'low') else current_price
                    open_price = ContextInfo.open[-1] if hasattr(ContextInfo, 'open') else current_price
                    volume = ContextInfo.vol[-1] if hasattr(ContextInfo, 'vol') else 0
                else:
                    # 方法2：使用get_market_data（tick模式使用lastPrice）
                    stock_code = ContextInfo.stockcode + '.' + ContextInfo.market
                    if STRATEGY_CONFIG.get('enable_data_buffer', False):
                        # Tick模式：使用lastPrice字段
                        market_data = ContextInfo.get_market_data(['lastPrice', 'lastVolume'], [stock_code])
                        if market_data and len(market_data) > 0:
                            data = market_data[0]
                            current_price = data.get('lastPrice', 0)
                            # Tick模式下，其他价格字段使用当前价格
                            high_price = current_price
                            low_price = current_price
                            open_price = current_price
                            volume = data.get('lastVolume', 0)
                        else:
                            print("⚠️ 无法获取tick市场数据")
                            return
                    else:
                        # K线模式：使用传统字段
                        market_data = ContextInfo.get_market_data(['close', 'high', 'low', 'open', 'volume'], [stock_code])
                        if market_data and len(market_data) > 0:
                            data = market_data[0]
                            current_price = data.get('close', 0)
                            high_price = data.get('high', current_price)
                            low_price = data.get('low', current_price)
                            open_price = data.get('open', current_price)
                            volume = data.get('volume', 0)
                        else:
                            print("⚠️ 无法获取K线市场数据")
                            return

            except Exception as e:
                print(f"⚠️ 获取K线数据失败: {e}")
                return

            # 获取时间戳
            try:
                timestamp = ContextInfo.get_bar_timetag(ContextInfo.barpos)
            except:
                timestamp = datetime.datetime.now()

            # 更新缓冲区（只在K线模式下）
            ContextInfo.data_buffer.update_realtime_data(
                close_price=current_price,
                high_price=high_price,
                low_price=low_price,
                open_price=open_price,
                volume=volume,
                timestamp=timestamp
            )

        # 计算技术指标（tick和K线模式都需要）
        ma5 = ContextInfo.data_buffer.calculate_ma(5)
        ma20 = ContextInfo.data_buffer.calculate_ma(20)
        atr = ContextInfo.data_buffer.calculate_atr(14)

        # 获取当前价格（从缓冲区）
        current_price = ContextInfo.data_buffer.get_current_price()
        if current_price is None:
            current_price = 0

        # 存储到上下文
        ContextInfo.ma5 = ma5
        ContextInfo.ma20 = ma20
        ContextInfo.atr = atr
        ContextInfo.current_price = current_price

        # 调试信息
        if getattr(ContextInfo, 'debug_mode', False):
            print(f"📊 数据更新: 价格={current_price:.3f}, 缓冲区={ContextInfo.data_buffer.get_data_count()}根K线")
            if ma5 and ma20:
                print(f"📈 指标: MA5={ma5:.3f}, MA20={ma20:.3f}, ATR={atr:.3f if atr else 'N/A'}")

    except Exception as e:
        print(f"❌ 缓冲区更新异常: {e}")
        import traceback
        traceback.print_exc()

def execute_trading_logic_with_indicators(ContextInfo):
    """
    执行交易逻辑 - 基于缓冲区数据和指标
    """
    try:
        # 获取指标数据
        ma5 = getattr(ContextInfo, 'ma5', None)
        ma20 = getattr(ContextInfo, 'ma20', None)
        atr = getattr(ContextInfo, 'atr', None)
        current_price = getattr(ContextInfo, 'current_price', None)

        if not all([ma5, ma20, current_price]):
            print("⚠️ 指标数据不完整，跳过交易逻辑")
            return

        print(f"\n📊 交易信号检查:")
        print(f"   当前价格: {current_price:.3f}")
        print(f"   MA5: {ma5:.3f}")
        print(f"   MA20: {ma20:.3f}")
        print(f"   ATR: {atr:.3f if atr else 'N/A'}")

        # 简单的均线交叉策略示例
        if ma5 > ma20 * 1.001:  # MA5 > MA20 且有一定幅度
            print(f"📈 买入信号: MA5({ma5:.3f}) > MA20({ma20:.3f})")
            # 这里可以调用买入函数
            # execute_buy_order_enhanced(ContextInfo, current_price)

        elif ma5 < ma20 * 0.999:  # MA5 < MA20 且有一定幅度
            print(f"📉 卖出信号: MA5({ma5:.3f}) < MA20({ma20:.3f})")
            # 这里可以调用卖出函数
            # execute_sell_order_enhanced(ContextInfo, current_price, "均线死叉")

        else:
            print(f"➡️ 无明确信号，继续观察")

    except Exception as e:
        print(f"❌ 交易逻辑执行异常: {e}")
        import traceback
        traceback.print_exc()

# ============================================================================
# 买入信号检查模块
# ============================================================================

def check_buy_signal_three_bars(ContextInfo, stock_code, market_data=None):
    """
    检查买入信号 - 接收三次数据后进行买入（无技术指标检查）

    参数:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码
        market_data: 市场数据（可选，当前版本不使用）

    返回:
        dict: 买入信号结果
    """
    try:
        if not STRATEGY_CONFIG['buy_signal_enabled']:
            return {
                'buy_signal': False,
                'reason': '买入信号检查已禁用',
                'data_count': 0
            }

        # 初始化策略状态
        if not hasattr(ContextInfo, 'strategy_state'):
            ContextInfo.strategy_state = {}

        if stock_code not in ContextInfo.strategy_state:
            ContextInfo.strategy_state[stock_code] = {
                'data_count': 0,
                'first_data_time': None,
                'buy_signal_triggered': False
            }

        state = ContextInfo.strategy_state[stock_code]

        # 增加数据接收计数
        state['data_count'] += 1

        # 记录第一次接收数据的时间
        if state['data_count'] == 1:
            state['first_data_time'] = ContextInfo.get_bar_timetag(ContextInfo.barpos)
            print(f"📊 开始接收数据: {stock_code} (第1次)")

        required_count = STRATEGY_CONFIG['buy_signal_count_required']
        current_count = state['data_count']

        print(f"📈 数据接收进度: {current_count}/{required_count}")

        # 检查是否达到要求的数据次数
        if current_count >= required_count and not state['buy_signal_triggered']:
            state['buy_signal_triggered'] = True

            # 直接触发买入信号，无需技术指标检查
            return {
                'buy_signal': True,
                'reason': f'接收{required_count}次数据完成，触发买入信号',
                'data_count': current_count,
                'first_data_time': state['first_data_time']
            }

        # 未达到要求次数
        return {
            'buy_signal': False,
            'reason': f'数据接收中 ({current_count}/{required_count})',
            'data_count': current_count,
            'remaining': required_count - current_count
        }

    except Exception as e:
        print(f"❌ 买入信号检查失败: {e}")
        return {
            'buy_signal': False,
            'reason': f'检查异常: {e}',
            'data_count': 0
        }

# ============================================================================
# 唐奇安通道计算模块
# ============================================================================

def calculate_donchian_channel(highs, lows, period=20):
    """
    计算唐奇安通道

    参数:
        highs: 最高价数组
        lows: 最低价数组
        period: 计算周期

    返回:
        dict: 唐奇安通道信息
    """
    try:
        highs = np.asarray(highs, dtype=np.float64)
        lows = np.asarray(lows, dtype=np.float64)

        if len(highs) < period:
            return {
                'upper_channel': highs[-1] if len(highs) > 0 else 100,
                'lower_channel': lows[-1] if len(lows) > 0 else 100,
                'middle_channel': (highs[-1] + lows[-1]) / 2 if len(highs) > 0 else 100,
                'period': period,
                'data_sufficient': False
            }

        # 计算唐奇安通道
        upper_channel = np.max(highs[-period:])  # 最高价
        lower_channel = np.min(lows[-period:])   # 最低价
        middle_channel = (upper_channel + lower_channel) / 2  # 中轨

        return {
            'upper_channel': upper_channel,
            'lower_channel': lower_channel,
            'middle_channel': middle_channel,
            'period': period,
            'data_sufficient': True,
            'channel_width': upper_channel - lower_channel,
            'channel_width_pct': (upper_channel - lower_channel) / middle_channel * 100 if middle_channel > 0 else 0
        }

    except Exception as e:
        print(f"❌ 唐奇安通道计算失败: {e}")
        return {
            'upper_channel': highs[-1] if len(highs) > 0 else 100,
            'lower_channel': lows[-1] if len(lows) > 0 else 100,
            'middle_channel': (highs[-1] + lows[-1]) / 2 if len(highs) > 0 else 100,
            'period': period,
            'data_sufficient': False
        }

def calculate_atr(highs, lows, closes, period=14):
    """
    计算平均真实波幅 (ATR)

    参数:
        highs: 最高价数组
        lows: 最低价数组
        closes: 收盘价数组
        period: ATR计算周期

    返回:
        dict: ATR信息
    """
    try:
        highs = np.asarray(highs, dtype=np.float64)
        lows = np.asarray(lows, dtype=np.float64)
        closes = np.asarray(closes, dtype=np.float64)

        if len(highs) < period + 1:
            # 数据不足时，使用保守的ATR估算
            if len(highs) >= 2:
                # 使用现有数据计算简单波幅
                price_range = max(highs) - min(lows)
                estimated_atr = price_range / len(highs) * 0.5  # 保守估算
                print(f"⚠️ ATR数据不足({len(highs)}个数据点，需要{period+1}个)，使用估算ATR: {estimated_atr:.4f}")
            else:
                estimated_atr = 0.01  # 最小保护值
                print(f"⚠️ ATR数据严重不足({len(highs)}个数据点)，使用最小保护值: {estimated_atr:.4f}")

            return {
                'atr': estimated_atr,
                'current_atr': estimated_atr,
                'data_sufficient': False,
                'period': period,
                'data_points': len(highs),
                'estimated': True
            }

        # 计算真实波幅 (True Range)
        tr_list = []
        for i in range(1, len(highs)):
            tr1 = highs[i] - lows[i]  # 当日最高价 - 当日最低价
            tr2 = abs(highs[i] - closes[i-1])  # 当日最高价 - 前日收盘价
            tr3 = abs(lows[i] - closes[i-1])   # 当日最低价 - 前日收盘价
            tr = max(tr1, tr2, tr3)
            tr_list.append(tr)

        tr_array = np.array(tr_list)

        # 计算ATR (简单移动平均)
        if len(tr_array) >= period:
            atr = np.mean(tr_array[-period:])
            current_atr = atr
        else:
            atr = np.mean(tr_array)
            current_atr = atr

        return {
            'atr': round(atr, 4),
            'current_atr': round(current_atr, 4),
            'data_sufficient': len(tr_array) >= period,
            'period': period,
            'tr_values': tr_list[-5:] if len(tr_list) >= 5 else tr_list  # 最近5个TR值
        }

    except Exception as e:
        print(f"❌ ATR计算失败: {e}")
        return {
            'atr': 0,
            'current_atr': 0,
            'data_sufficient': False,
            'period': period
        }

def calculate_dynamic_trigger_threshold(closes, period=20):
    """
    计算动态移动止盈触发阈值

    参数:
        closes: 收盘价数组
        period: 波动率计算周期

    返回:
        dict: 动态阈值信息
    """
    try:
        closes = np.asarray(closes, dtype=np.float64)

        if len(closes) < period:
            # 数据不足，使用默认值
            return {
                'trigger_threshold': STRATEGY_CONFIG['trailing_trigger_pct'],
                'volatility': 0,
                'volatility_level': 'unknown',
                'data_sufficient': False
            }

        # 计算历史波动率 (标准差)
        returns = np.diff(closes[-period:]) / closes[-period:-1]
        volatility = np.std(returns) * 100  # 转换为百分比

        # 根据波动率动态调整触发阈值
        low_vol_threshold = STRATEGY_CONFIG['low_volatility_trigger']
        high_vol_threshold = STRATEGY_CONFIG['high_volatility_trigger']

        # 波动率分级
        if volatility < 1.5:
            volatility_level = 'low'
            trigger_threshold = low_vol_threshold
        elif volatility > 4.0:
            volatility_level = 'high'
            trigger_threshold = high_vol_threshold
        else:
            volatility_level = 'medium'
            # 线性插值
            ratio = (volatility - 1.5) / (4.0 - 1.5)
            trigger_threshold = low_vol_threshold + ratio * (high_vol_threshold - low_vol_threshold)

        return {
            'trigger_threshold': round(trigger_threshold, 1),
            'volatility': round(volatility, 2),
            'volatility_level': volatility_level,
            'data_sufficient': True
        }

    except Exception as e:
        print(f"❌ 动态阈值计算失败: {e}")
        return {
            'trigger_threshold': STRATEGY_CONFIG['trailing_trigger_pct'],
            'volatility': 0,
            'volatility_level': 'error',
            'data_sufficient': False
        }

def calculate_moving_profit_control(highs, lows, closes):
    """
    计算移动止盈风控 - 基于动态ATR的优化移动止盈机制
    
    参数:
        highs: 最高价数组
        lows: 最低价数组  
        closes: 收盘价数组
        
    返回:
        dict: 优化移动止盈风控信息
    """
    try:
        ATR_PERIOD = STRATEGY_CONFIG['atr_period']
        base_multiplier = STRATEGY_CONFIG['atr_multiplier']
        
        if len(closes) < ATR_PERIOD + 5:
            return {
                'ATR_周期': ATR_PERIOD,
                'ATR值': 0.05,
                '止损距离': 0.25,
                '初始止盈距离': 0.25,
                '移动触发距离': 0.25,
                '加速移动距离': 0.175,
                '最小移动幅度': STRATEGY_CONFIG['min_move_threshold'],
                '当前价格': closes[-1] if len(closes) > 0 else 100,
                '市场模式': '统一21日ATR',
                '波动率区间': '正常波动区',
                '计算模式': '优化移动止盈方案(数据不足)'
            }
        
        # 计算ATR
        tr_values = calculate_true_range(highs, lows, closes)
        atr_value = np.mean(tr_values[-ATR_PERIOD:])
        current_atr = atr_value
        current_price = closes[-1]

        # 计算ATR百分比
        atr_percentage = (current_atr / current_price * 100) if current_price > 0 else 0.05

        # 根据优化移动止盈方案计算距离
        stop_loss_distance = atr_percentage * base_multiplier
        initial_profit_distance = atr_percentage * base_multiplier

        # 加速移动机制
        acceleration_multiplier = STRATEGY_CONFIG['accelerated_multiplier']
        acceleration_trigger_distance = atr_percentage * acceleration_multiplier
        
        # 最小移动限制
        min_move_threshold = STRATEGY_CONFIG['min_move_threshold']

        # 合理性检查
        min_distance = 0.15
        max_distance = 5.0

        stop_loss_distance = max(min(stop_loss_distance, max_distance), min_distance)
        initial_profit_distance = max(min(initial_profit_distance, max_distance), min_distance)
        
        # 波动率区间判断
        if atr_percentage < 0.3:
            volatility_zone = '低波动区'
        elif atr_percentage < 0.8:
            volatility_zone = '正常波动区'
        elif atr_percentage < 1.5:
            volatility_zone = '高波动区'
        else:
            volatility_zone = '极高波动区'
            
        return {
            'ATR_周期': ATR_PERIOD,
            'ATR值': current_atr,
            'ATR_百分比': atr_percentage,
            '止损距离': stop_loss_distance,
            '初始止盈距离': initial_profit_distance,
            '标准移动触发距离': initial_profit_distance,
            '加速移动触发距离': acceleration_trigger_distance,
            '最小移动幅度': min_move_threshold,
            '当前价格': current_price,
            '市场模式': '统一21日ATR',
            '波动率区间': volatility_zone,
            '计算模式': '优化移动止盈方案'
        }
        
    except Exception as e:
        print(f"⚠️ 移动止盈风控计算失败: {e}")
        return {
            '止损距离': 0.23,
            '初始止盈距离': 0.23,
            '移动触发距离': 0.23,
            '当前价格': closes[-1] if len(closes) > 0 else 100,
            '波动率区间': '正常波动区',
            '计算模式': '移动止盈方案(默认)'
        }

def calculate_true_range(highs, lows, closes):
    """计算真实波动范围（True Range）"""
    try:
        highs = np.asarray(highs, dtype=np.float64)
        lows = np.asarray(lows, dtype=np.float64)
        closes = np.asarray(closes, dtype=np.float64)
        
        data_len = len(closes)
        if data_len < 2:
            return np.zeros(data_len)
        
        # HL = HIGH - LOW
        hl = highs - lows
        
        # HC = ABS(HIGH - REF(CLOSE,1))
        hc = np.zeros(data_len, dtype=np.float64)
        hc[0] = hl[0]
        for i in range(1, data_len):
            hc[i] = abs(highs[i] - closes[i-1])
        
        # LC = ABS(REF(CLOSE,1) - LOW)
        lc = np.zeros(data_len, dtype=np.float64)
        lc[0] = hl[0]
        for i in range(1, data_len):
            lc[i] = abs(closes[i-1] - lows[i])
        
        # TR = MAX(MAX(HL, HC), LC)
        tr = np.maximum(np.maximum(hl, hc), lc)
        
        return tr
        
    except Exception as e:
        print(f"❌ True Range计算失败: {e}")
        return np.zeros(len(closes))

# ============================================================================
# 止盈止损检查模块
# ============================================================================

def check_exit_conditions_donchian(ContextInfo, current_price, entry_price, highest_price_since_entry, trailing_stop_price, market_data=None):
    """
    检查平仓条件 - 基于唐奇安通道和ATR的移动止盈止损

    参数:
        ContextInfo: QMT上下文对象
        current_price: 当前价格
        entry_price: 入场价格
        highest_price_since_entry: 入场后最高价
        trailing_stop_price: 移动止盈价格
        market_data: 市场数据 {'highs': [], 'lows': [], 'closes': []}

    返回:
        dict: 平仓决策结果
    """
    if entry_price <= 0:
        return {'should_exit': False, 'reason': '无有效入场价格', 'new_trailing_stop': trailing_stop_price}

    # 计算盈亏比例
    profit_pct = (current_price - entry_price) / entry_price

    # 更新最高价
    new_highest_price = max(highest_price_since_entry, current_price)
    new_trailing_stop = trailing_stop_price

    # 计算ATR
    atr_info = {'atr': 0, 'data_sufficient': False}
    if market_data and 'highs' in market_data and 'lows' in market_data and 'closes' in market_data:
        atr_info = calculate_atr(
            market_data['highs'],
            market_data['lows'],
            market_data['closes'],
            STRATEGY_CONFIG['atr_period']
        )

    # ATR动态止损检查
    if atr_info['data_sufficient'] and atr_info['atr'] > 0:
        atr_stop_loss = entry_price - (atr_info['atr'] * STRATEGY_CONFIG['atr_stop_loss_multiplier'])
        if current_price <= atr_stop_loss:
            return {
                'should_exit': True,
                'reason': f'ATR动态止损触发 (ATR={atr_info["atr"]:.3f}, 止损价={atr_stop_loss:.3f})',
                'price': current_price,
                'new_trailing_stop': new_trailing_stop,
                'new_highest_price': new_highest_price,
                'atr_info': atr_info
            }
    else:
        # 如果ATR数据不足，使用固定止损
        fixed_stop_loss_pct = STRATEGY_CONFIG['fixed_stop_loss'] / 100
        if profit_pct <= -fixed_stop_loss_pct:
            return {
                'should_exit': True,
                'reason': f'固定止损触发 ({profit_pct:.2%})',
                'price': current_price,
                'new_trailing_stop': new_trailing_stop,
                'new_highest_price': new_highest_price
            }

    # 计算唐奇安通道
    donchian_info = {'data_sufficient': False}
    if market_data and 'highs' in market_data and 'lows' in market_data:
        donchian_info = calculate_donchian_channel(
            market_data['highs'],
            market_data['lows'],
            STRATEGY_CONFIG['donchian_period']
        )

    # 唐奇安通道移动止盈逻辑
    if STRATEGY_CONFIG['use_trailing_stop'] and market_data and donchian_info['data_sufficient']:
        # 计算移动止盈触发阈值
        if atr_info['data_sufficient'] and atr_info['atr'] > 0:
            # 使用ATR计算触发阈值
            atr_trigger_threshold = atr_info['atr'] * STRATEGY_CONFIG['atr_trigger_multiplier']
        else:
            # ATR数据不足时，使用固定百分比作为触发阈值
            atr_trigger_threshold = entry_price * 0.005  # 0.5%作为默认触发阈值
            print(f"⚠️ ATR数据不足，使用固定触发阈值: {atr_trigger_threshold:.3f} (0.5%)")

        profit_amount = current_price - entry_price

        # 确保触发阈值不为0，避免任何微小利润都触发
        if atr_trigger_threshold <= 0:
            atr_trigger_threshold = entry_price * 0.01  # 最小1%触发阈值
            print(f"⚠️ 触发阈值为0，使用最小保护阈值: {atr_trigger_threshold:.3f} (1.0%)")

        print(f"🔍 移动止盈检查: 利润={profit_amount:.3f}, 触发阈值={atr_trigger_threshold:.3f}")

        if profit_amount >= atr_trigger_threshold:
            print(f"✅ 达到移动止盈触发条件，开始跟踪唐奇安下轨")
            # 直接使用唐奇安下轨作为移动止盈线 (移除2%偏移)
            donchian_stop = donchian_info['lower_channel']

            # 移动止盈线只能向上移动
            if donchian_stop > new_trailing_stop:
                new_trailing_stop = donchian_stop
                print(f"📈 更新移动止盈线: {new_trailing_stop:.3f}")

            # 检查是否触发移动止盈 (收盘价小于唐奇安下轨)
            if new_trailing_stop > 0 and current_price <= new_trailing_stop:
                protected_profit = (new_trailing_stop - entry_price) / entry_price * 100
                return {
                    'should_exit': True,
                    'reason': f'唐奇安移动止盈触发 (保护{protected_profit:.1f}%利润, ATR触发阈值={atr_trigger_threshold:.3f})',
                    'price': current_price,
                    'new_trailing_stop': new_trailing_stop,
                    'new_highest_price': new_highest_price,
                    'atr_info': atr_info,
                    'donchian_info': donchian_info
                }
        else:
            print(f"⏳ 未达到移动止盈触发条件，继续等待")

    # 打印调试信息
    print(f"🔍 ATR唐奇安移动止盈检查:")
    print(f"   💰 当前价格: {current_price:.3f}")
    print(f"   📊 入场价格: {entry_price:.3f}")
    print(f"   📈 盈亏比例: {profit_pct:.2%}")

    if atr_info['data_sufficient']:
        atr_trigger_threshold = atr_info['atr'] * STRATEGY_CONFIG['atr_trigger_multiplier']
        print(f"   📊 ATR值: {atr_info['atr']:.3f}")
        print(f"   🎯 ATR触发阈值: {atr_trigger_threshold:.3f}")
        print(f"   🛡️ ATR止损价: {entry_price - (atr_info['atr'] * STRATEGY_CONFIG['atr_stop_loss_multiplier']):.3f}")

    print(f"   📊 当前移动止盈线: {new_trailing_stop:.3f}")

    if market_data and donchian_info['data_sufficient']:
        print(f"   📈 唐奇安上轨: {donchian_info['upper_channel']:.3f}")
        print(f"   📉 唐奇安下轨: {donchian_info['lower_channel']:.3f}")
        print(f"   📊 通道宽度: {donchian_info['channel_width_pct']:.2f}%")

    # 继续持仓
    return {
        'should_exit': False,
        'reason': f'继续持仓 (盈亏: {profit_pct:.2%}, ATR={atr_info.get("atr", 0):.3f})',
        'new_trailing_stop': new_trailing_stop,
        'new_highest_price': new_highest_price,
        'atr_info': atr_info,
        'donchian_info': donchian_info
    }

# ============================================================================
# 委托查询和撤单模块
# ============================================================================

def debug_order_object(order_obj, index=0):
    """
    调试委托对象，显示所有可用字段和值
    """
    print(f"\n🔍 委托对象 #{index+1} 详细信息:")
    print("-" * 40)

    # 获取所有非私有属性
    all_attrs = [attr for attr in dir(order_obj) if not attr.startswith('_') and not callable(getattr(order_obj, attr))]

    # 显示关键字段
    key_fields = ['m_strOrderSysID', 'm_strInstrumentID', 'm_strInstrumentName',
                  'm_nDirection', 'm_nOffsetFlag', 'm_strOptName', 'm_eEntrustType',
                  'm_eOrderStatus', 'm_dPrice', 'm_nVolume', 'm_nVolumeTraded']

    print("📋 关键字段:")
    for field in key_fields:
        if hasattr(order_obj, field):
            value = getattr(order_obj, field)
            print(f"   {field}: {value} ({type(value).__name__})")
        else:
            print(f"   {field}: [不存在]")

    print(f"\n📋 所有字段 (共{len(all_attrs)}个):")
    for i, attr in enumerate(all_attrs):
        try:
            value = getattr(order_obj, attr)
            print(f"   {i+1:2d}. {attr}: {value}")
        except Exception as e:
            print(f"   {i+1:2d}. {attr}: [获取失败: {e}]")

    print("-" * 40)

def query_orders(ContextInfo, stock_code=None, order_type='ALL'):
    """
    查询委托信息

    参数:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码，None表示查询所有
        order_type: 订单类型 ('BUY', 'SELL', 'ALL')

    返回:
        list: 委托信息列表
    """
    try:
        # 使用正确的账户ID属性
        account_id = getattr(ContextInfo, 'acct', '*********')
        orders = get_trade_detail_data(account_id, 'stock', 'order')
        if not orders:
            print("📋 没有找到委托信息")
            return []

        # 预过滤：根据配置过滤不需要的委托
        pre_filtered_orders = []
        total_orders = len(orders)

        for order_obj in orders:
            order_status = getattr(order_obj, 'm_nOrderStatus', -1)

            # 快速状态判断
            is_cancelled = order_status in [53, 54]  # 已撤销、部分撤销
            is_completed = order_status == 52        # 全部成交

            # 根据配置过滤
            should_include = True

            if STRATEGY_CONFIG.get('filter_cancelled_orders', True) and is_cancelled:
                should_include = False  # 过滤已撤销的委托

            if STRATEGY_CONFIG.get('filter_completed_orders', True) and is_completed:
                should_include = False  # 过滤已完成的委托

            if should_include:
                pre_filtered_orders.append(order_obj)

        print(f"📋 总委托: {total_orders}个, 有效委托: {len(pre_filtered_orders)}个")
        if STRATEGY_CONFIG.get('filter_cancelled_orders', True):
            print("🔍 已过滤已撤销委托")
        if STRATEGY_CONFIG.get('filter_completed_orders', True):
            print("🔍 已过滤已完成委托")

        if not pre_filtered_orders:
            print("📋 没有有效的委托信息")
            return []

        filtered_orders = []

        for i, order_obj in enumerate(pre_filtered_orders):
            try:

                # 获取委托信息
                order_status = getattr(order_obj, 'm_nOrderStatus', -1)
                order_op_type = getattr(order_obj, 'm_nOffsetFlag', -1)  # 23=买入, 24=卖出

                # 调试信息：显示所有可能的方向字段
                direction_field = getattr(order_obj, 'm_nDirection', -1)
                print(f"🔍 委托方向调试: m_nOffsetFlag={order_op_type}, m_nDirection={direction_field}")
                order_stock = getattr(order_obj, 'm_strInstrumentID', '') + '.' + getattr(order_obj, 'm_strExchangeID', '')
                order_volume = getattr(order_obj, 'm_nVolumeTotalOriginal', 0)
                traded_volume = getattr(order_obj, 'm_nVolumeTraded', 0)
                order_price = getattr(order_obj, 'm_dLimitPrice', 0)
                order_id = getattr(order_obj, 'm_strOrderSysID', '')
                order_time = getattr(order_obj, 'm_strInsertTime', '')

                # 过滤条件
                if stock_code and order_stock != stock_code:
                    continue

                if order_type == 'BUY' and order_op_type != 1:
                    continue
                elif order_type == 'SELL' and order_op_type != 2:
                    continue

                # 状态描述（基于实际运行数据修正）
                status_desc = {
                    -1: '状态待确认',      # 🔧 修正：-1状态需要进一步判断
                    48: '未报',
                    49: '待报',
                    50: '已报待成交',
                    51: '部分成交',
                    52: '全部成交',
                    53: '已撤销',
                    54: '部分撤销'
                }.get(order_status, f'未知状态({order_status})')

                if STRATEGY_CONFIG.get('debug_status_mapping', True):
                    print(f"🔍 状态映射: Status={order_status} → {status_desc}")

                # 🔧 对于Status=-1，根据配置决定如何处理
                if order_status == -1:
                    if STRATEGY_CONFIG.get('status_minus_one_as_pending', False):
                        # 用户选择将-1视为未处理状态
                        if traded_volume > 0 and traded_volume >= order_volume:
                            status_desc = '全部成交'
                            actual_status = 52
                        elif traded_volume > 0:
                            status_desc = '部分成交'
                            actual_status = 51
                        else:
                            status_desc = '已报待成交'
                            actual_status = 50
                        print(f"🔍 Status=-1 处理: 成交量{traded_volume}/{order_volume} → {status_desc} (配置为pending)")
                    else:
                        # 默认：将-1视为非未处理状态（保守处理）
                        status_desc = '状态不明(已处理)'
                        actual_status = 53  # 视为已撤销
                        print(f"🔍 Status=-1 处理: 保守处理为已撤销状态 (配置为non-pending)")
                else:
                    actual_status = order_status

                # QMT委托方向代码映射（基于官方文档）
                # QMT委托方向识别（基于实际运行结果修正）
                direction_field = getattr(order_obj, 'm_nDirection', -1)

                # 获取更多字段用于判断买卖方向
                order_status = getattr(order_obj, 'm_eOrderStatus', -1)
                opt_name = getattr(order_obj, 'm_strOptName', '')
                entrust_type = getattr(order_obj, 'm_eEntrustType', -1)

                print(f"🔍 详细字段: OptName='{opt_name}', EntrustType={entrust_type}, Status={order_status}")

                # 🔧 基于实际运行数据的方向识别（修复版）
                # 根据用户反馈：OptName='限价买入', EntrustType=48

                # 方法1：通过m_strOptName字段判断（中文描述）- 最可靠 ✅ 已确认有效
                if '买' in opt_name or '买入' in opt_name:
                    op_type_desc = '买入'
                    print(f"✅ 通过OptName识别: '{opt_name}' → 买入")
                elif '卖' in opt_name or '卖出' in opt_name:
                    op_type_desc = '卖出'
                    print(f"✅ 通过OptName识别: '{opt_name}' → 卖出")
                # 方法2：通过标准的EOffset_Flag_Type枚举
                elif order_op_type in [23, '23']:
                    op_type_desc = '买入'
                elif order_op_type in [24, '24']:
                    op_type_desc = '卖出'
                # 方法3：通过EntrustType判断
                elif entrust_type == 1:  # 可能的买入类型
                    op_type_desc = '买入'
                elif entrust_type == 2:  # 可能的卖出类型
                    op_type_desc = '卖出'
                # 方法4：基于实际数据的特殊处理
                elif order_op_type == 48 and direction_field == 48:
                    # 根据价格与当前市价比较推测方向
                    try:
                        # 使用lastPrice字段获取当前价格（适配tick数据）
                        current_price = ContextInfo.get_market_data(['lastPrice'], [order_stock])
                        if current_price and order_stock in current_price:
                            market_price = current_price[order_stock]['lastPrice']
                            price_diff_pct = (order_price - market_price) / market_price

                            if price_diff_pct > 0.005:  # 高于市价0.5%以上，可能是卖出
                                op_type_desc = '卖出(价格推测)'
                            elif price_diff_pct < -0.005:  # 低于市价0.5%以上，可能是买入
                                op_type_desc = '买入(价格推测)'
                            else:
                                op_type_desc = f'方向待确定(接近市价,offset={order_op_type})'
                        else:
                            op_type_desc = f'方向待确定(无市价,offset={order_op_type})'
                    except:
                        op_type_desc = f'方向待确定(offset={order_op_type},dir={direction_field})'
                # 方法5：如果都无法判断
                else:
                    op_type_desc = f'未知方向(offset={order_op_type},dir={direction_field},type={entrust_type})'

                order_info = {
                    'order_id': order_id,
                    'stock': order_stock,
                    'op_type': order_op_type,
                    'op_type_desc': op_type_desc,
                    'status': order_status,
                    'status_desc': status_desc,
                    'price': order_price,
                    'total_volume': order_volume,
                    'traded_volume': traded_volume,
                    'remaining_volume': order_volume - traded_volume,
                    'order_time': order_time,
                    'is_pending': actual_status in STRATEGY_CONFIG.get('pending_status_codes', [48, 49, 50, 51]),
                    'is_completed': actual_status == 52,
                    'is_cancelled': actual_status in [53, 54]
                }

                # 🔍 调试：显示委托状态判断结果
                if STRATEGY_CONFIG.get('debug_status_mapping', True):
                    print(f"🔍 委托状态判断: 原始Status={order_status}, 修正Status={actual_status} → is_pending={order_info['is_pending']}, is_completed={order_info['is_completed']}")

                filtered_orders.append(order_info)

            except Exception as e:
                print(f"❌ 处理委托信息异常: {e}")
                continue

        return filtered_orders

    except Exception as e:
        print(f"❌ 查询委托异常: {e}")
        return []

def print_orders_summary(orders):
    """打印委托摘要信息"""
    if not orders:
        print("📋 无委托信息")
        return

    print(f"📋 委托信息摘要 (共{len(orders)}个):")
    print("=" * 80)
    print(f"{'序号':<4} {'委托号':<12} {'股票':<12} {'方向':<6} {'状态':<10} {'价格':<8} {'数量':<8} {'成交':<8}")
    print("-" * 80)

    for i, order in enumerate(orders, 1):
        print(f"{i:<4} {order['order_id']:<12} {order['stock']:<12} {order['op_type_desc']:<6} "
              f"{order['status_desc']:<10} {order['price']:<8.3f} {order['total_volume']:<8} {order['traded_volume']:<8}")

def cancel_orders(ContextInfo, stock_code=None, order_type='ALL', order_ids=None):
    """
    撤销委托

    参数:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码，None表示所有股票
        order_type: 订单类型 ('BUY', 'SELL', 'ALL')
        order_ids: 指定的订单ID列表，None表示按条件撤销

    返回:
        dict: 撤单结果统计
    """
    cancel_stats = {
        'total_checked': 0,
        'cancelled_count': 0,
        'cancel_success': 0,
        'cancel_failed': 0,
        'errors': []
    }

    try:
        # 如果指定了订单ID，直接撤销
        if order_ids:
            for order_id in order_ids:
                try:
                    account_id = getattr(ContextInfo, 'acct', '*********')
                    cancel_result = cancel(order_id, account_id, 'STOCK', ContextInfo)
                    if cancel_result:
                        cancel_stats['cancel_success'] += 1
                        print(f"✅ 撤单成功: ID={order_id}")
                    else:
                        cancel_stats['cancel_failed'] += 1
                        print(f"❌ 撤单失败: ID={order_id}")
                except Exception as e:
                    cancel_stats['cancel_failed'] += 1
                    cancel_stats['errors'].append(f"撤单异常: {e}")
                    print(f"❌ 撤单异常: {e}")

            cancel_stats['cancelled_count'] = len(order_ids)
            return cancel_stats

        # 按条件查询并撤销
        orders = query_orders(ContextInfo, stock_code, order_type)
        cancel_stats['total_checked'] = len(orders)

        if not orders:
            print("📋 没有找到符合条件的委托")
            return cancel_stats

        # 筛选需要撤销的订单
        orders_to_cancel = [order for order in orders if order['is_pending']]

        if not orders_to_cancel:
            print("📋 没有找到需要撤销的委托")
            return cancel_stats

        print(f"🔄 准备撤销 {len(orders_to_cancel)} 个委托")

        for order in orders_to_cancel:
            try:
                cancel_stats['cancelled_count'] += 1
                print(f"🔄 撤销委托: {order['stock']} {order['op_type_desc']} {order['total_volume']}股@{order['price']:.3f}")

                account_id = getattr(ContextInfo, 'acct', '*********')
                cancel_result = cancel(order['order_id'], account_id, 'STOCK', ContextInfo)
                if cancel_result:
                    cancel_stats['cancel_success'] += 1
                    print(f"✅ 撤单成功: ID={order['order_id']}")
                else:
                    cancel_stats['cancel_failed'] += 1
                    print(f"❌ 撤单失败: ID={order['order_id']}")

            except Exception as e:
                cancel_stats['cancel_failed'] += 1
                cancel_stats['errors'].append(f"撤单异常: {e}")
                print(f"❌ 撤单异常: {e}")

        return cancel_stats

    except Exception as e:
        cancel_stats['errors'].append(f"撤单流程异常: {e}")
        print(f"❌ 撤单流程异常: {e}")
        return cancel_stats

def check_pending_orders(ContextInfo, stock_code, order_type='ALL'):
    """
    检查是否有未处理的订单

    参数:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码
        order_type: 订单类型 ('BUY', 'SELL', 'ALL')

    返回:
        bool: True表示有未处理订单
    """
    try:
        orders = query_orders(ContextInfo, stock_code, order_type)
        pending_orders = [order for order in orders if order['is_pending']]

        if pending_orders:
            print(f"⚠️ 发现 {len(pending_orders)} 个未处理订单:")
            for order in pending_orders:
                print(f"   - {order['op_type_desc']} {order['total_volume']}股@{order['price']:.3f} [{order['status_desc']}]")
            return True

        return False

    except Exception as e:
        print(f"❌ 检查未处理订单异常: {e}")
        return False

# ============================================================================
# 买入订单执行模块
# ============================================================================

def execute_buy_order(ContextInfo, stock_code, current_price, volume=None, auto_cancel_pending=True):
    """
    执行买入订单 - 带挂单偏移策略

    参数:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码
        current_price: 当前价格
        volume: 买入数量，如果为None则自动计算
        auto_cancel_pending: 是否自动撤销未处理的买入订单

    返回:
        dict: 买入结果
    """
    try:
        print(f"\n📤 准备执行买入订单: {stock_code}")

        # 检查并撤销未处理的买入订单
        if auto_cancel_pending and check_pending_orders(ContextInfo, stock_code, 'BUY'):
            print("🔄 自动撤销未处理的买入订单")
            cancel_result = cancel_orders(ContextInfo, stock_code, 'BUY')
            print(f"   撤单结果: 成功{cancel_result['cancel_success']}个，失败{cancel_result['cancel_failed']}个")

            # 等待撤单完成
            time.sleep(1)

        # 获取账户信息
        account_id = getattr(ContextInfo, 'acct', '*********')
        account_info = get_trade_detail_data(account_id, 'stock', 'account')
        if not account_info:
            return {'success': False, 'reason': '无法获取账户信息'}

        available_cash = getattr(account_info[0], 'm_dAvailable', 0)
        print(f"💰 可用资金: {available_cash:.2f}")

        # 检查最小资金保留
        if available_cash <= STRATEGY_CONFIG['min_cash_reserve']:
            return {'success': False, 'reason': f'资金不足，低于最小保留{STRATEGY_CONFIG["min_cash_reserve"]}'}

        # 计算买入数量
        if volume is None:
            # 自动计算买入数量
            max_investment = (available_cash - STRATEGY_CONFIG['min_cash_reserve']) * STRATEGY_CONFIG['max_position_ratio']
            volume = int(max_investment / current_price / 10) * 10  # 整10股（1手=10股）

        if volume <= 0:
            return {'success': False, 'reason': '计算买入数量为0'}

        # 检查资金是否充足
        required_cash = current_price * volume * 1.001  # 加上手续费余量
        if required_cash > available_cash:
            return {'success': False, 'reason': f'资金不足，需要{required_cash:.2f}，可用{available_cash:.2f}'}

        # 计算挂单价格（支持正负偏移调整）
        offset_ratio = STRATEGY_CONFIG['buy_hang_offset_ratio']
        hang_offset = float(current_price) * abs(offset_ratio)

        if offset_ratio >= 0:
            # 正值：低于当前价格买入（保守策略）
            hang_price = float(current_price) - hang_offset
            offset_desc = f"-{hang_offset:.3f}"
        else:
            # 负值：高于当前价格买入（激进策略）
            hang_price = float(current_price) + hang_offset
            offset_desc = f"+{hang_offset:.3f}"

        hang_price = round(hang_price, 3)

        print(f"📊 买入参数:")
        print(f"   当前价格: {current_price:.3f}")
        print(f"   挂单价格: {hang_price:.3f} (偏移: {offset_desc})")
        print(f"   偏移比例: {offset_ratio:.4f} ({'保守策略' if offset_ratio >= 0 else '激进策略'})")
        print(f"   买入数量: {volume}股")
        print(f"   预计金额: {hang_price * volume:.2f}")

        # 执行买入订单
        order_msg = f"买入{stock_code} {volume}股@{hang_price:.3f}"

        # 执行买入订单 - 参考6sk线的调用方式
        order_result = passorder(
            23,                     # 买入代码
            1101,                   # 委托类型
            ContextInfo.acct,       # 账户ID
            ContextInfo.stock,      # 股票代码
            11,                     # 限价单
            hang_price,             # 挂单价格
            volume,                 # 数量
            '止盈止损策略',          # 策略名称
            1,                      # 立即下单
            order_msg,              # 委托备注
            ContextInfo             # 上下文
        )

        # QMT下单返回值判断（基于官方文档）
        print(f"📋 下单返回值: {order_result} (类型: {type(order_result)})")

        # 根据QMT官方文档：passorder函数返回None，不返回订单ID
        # 需要通过get_last_order_id来获取最新的订单ID
        print(f"✅ 买入订单已提交到QMT")
        print(f"⚠️  QMT下单是异步的，正在获取订单ID...")

        # 等待一小段时间让QMT处理订单
        import time
        time.sleep(0.5)

        # 获取最新的订单ID（使用QMT内置函数）
        try:
            # QMT内置函数，不需要导入
            latest_order_id = get_last_order_id(ContextInfo.acct, 'stock', 'order', '止盈止损策略')

            if latest_order_id and latest_order_id != '-1':
                print(f"✅ 获取到订单ID: {latest_order_id}")
                return {
                    'success': True,
                    'order_id': str(latest_order_id),
                    'stock_code': stock_code,
                    'price': hang_price,
                    'volume': volume,
                    'amount': hang_price * volume,
                    'action': 'buy'
                }
            else:
                print(f"⚠️  暂未获取到订单ID，但订单可能已提交")
                return {
                    'success': True,  # 假设成功，因为passorder没有报错
                    'order_id': 'pending',
                    'stock_code': stock_code,
                    'price': hang_price,
                    'volume': volume,
                    'amount': hang_price * volume,
                    'action': 'buy'
                }
        except Exception as e:
            print(f"⚠️  获取订单ID时出错: {e}")
            return {
                'success': True,  # 假设成功，因为passorder没有报错
                'order_id': 'unknown',
                'stock_code': stock_code,
                'price': hang_price,
                'volume': volume,
                'amount': hang_price * volume,
                'action': 'buy'
            }

    except Exception as e:
        print(f"❌ 买入订单执行异常: {e}")
        return {'success': False, 'reason': f'执行异常: {e}'}

# ============================================================================
# 卖出订单执行模块
# ============================================================================

def execute_sell_order(ContextInfo, stock_code, current_price, volume=None, reason="手动卖出", auto_cancel_pending=True):
    """
    执行卖出订单 - 带挂单偏移策略

    参数:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码
        current_price: 当前价格
        volume: 卖出数量，如果为None则卖出全部持仓
        reason: 卖出原因
        auto_cancel_pending: 是否自动撤销未处理的卖出订单

    返回:
        dict: 卖出结果
    """
    try:
        print(f"\n📤 准备执行卖出订单: {stock_code}")
        print(f"   卖出原因: {reason}")

        # 检查并撤销未处理的卖出订单
        if auto_cancel_pending and check_pending_orders(ContextInfo, stock_code, 'SELL'):
            print("🔄 自动撤销未处理的卖出订单")
            cancel_result = cancel_orders(ContextInfo, stock_code, 'SELL')
            print(f"   撤单结果: 成功{cancel_result['cancel_success']}个，失败{cancel_result['cancel_failed']}个")

            # 等待撤单完成
            time.sleep(1)

        # 获取持仓信息
        account_id = getattr(ContextInfo, 'acct', '*********')
        position_info = get_trade_detail_data(account_id, 'stock', 'position')
        if not position_info:
            return {'success': False, 'reason': '无法获取持仓信息'}

        # 查找当前股票的持仓
        current_position = None
        for pos in position_info:
            pos_stock = getattr(pos, 'm_strInstrumentID', '') + '.' + getattr(pos, 'm_strExchangeID', '')
            if pos_stock == stock_code:
                current_position = pos
                break

        if not current_position:
            return {'success': False, 'reason': '未找到当前股票持仓'}

        available_volume = getattr(current_position, 'm_nVolume', 0)
        if available_volume <= 0:
            return {'success': False, 'reason': '可用持仓数量为0'}

        # 确定卖出数量
        if volume is None:
            volume = available_volume
        else:
            volume = min(volume, available_volume)

        if volume <= 0:
            return {'success': False, 'reason': '卖出数量为0'}

        # 计算挂单价格（支持正负偏移调整）
        offset_ratio = STRATEGY_CONFIG['sell_hang_offset_ratio']
        hang_offset = float(current_price) * abs(offset_ratio)

        if offset_ratio >= 0:
            # 正值：高于当前价格卖出（保守策略）
            hang_price = float(current_price) + hang_offset
            offset_desc = f"+{hang_offset:.3f}"
        else:
            # 负值：低于当前价格卖出（激进策略）
            hang_price = float(current_price) - hang_offset
            offset_desc = f"-{hang_offset:.3f}"

        hang_price = round(hang_price, 3)

        print(f"📊 卖出参数:")
        print(f"   当前价格: {current_price:.3f}")
        print(f"   挂单价格: {hang_price:.3f} (偏移: {offset_desc})")
        print(f"   偏移比例: {offset_ratio:.4f} ({'保守策略' if offset_ratio >= 0 else '激进策略'})")
        print(f"   卖出数量: {volume}股 (可用: {available_volume}股)")
        print(f"   预计金额: {hang_price * volume:.2f}")

        # 执行卖出订单
        order_msg = f"卖出{stock_code} {volume}股@{hang_price:.3f} ({reason})"

        # 执行卖出订单 - 参考6sk线的调用方式
        order_result = passorder(
            24,                     # 卖出代码
            1101,                   # 委托类型
            ContextInfo.acct,       # 账户ID
            ContextInfo.stock,      # 股票代码
            11,                     # 限价单
            hang_price,             # 挂单价格
            volume,                 # 数量
            '止盈止损策略',          # 策略名称
            1,                      # 立即下单
            order_msg,              # 委托备注
            ContextInfo             # 上下文
        )

        # QMT下单返回值判断优化
        print(f"📋 下单返回值: {order_result} (类型: {type(order_result)})")

        if order_result and str(order_result) not in ['0', '-1', '', 'None']:
            print(f"✅ 卖出订单提交成功")
            print(f"   订单ID: {order_result}")

            return {
                'success': True,
                'order_id': str(order_result),  # 确保订单ID是字符串
                'stock_code': stock_code,
                'price': hang_price,
                'volume': volume,
                'amount': hang_price * volume,
                'reason': reason,
                'action': 'sell'
            }
        else:
            print(f"❌ 卖出订单可能提交失败，返回值: {order_result}")
            print(f"⚠️  注意：QMT有时返回0但订单实际已提交，请检查委托列表确认")

            # 详细的失败原因分析
            failure_reason = "未知原因"
            if order_result == 0:
                failure_reason = "QMT返回0 - 可能是订单已提交但返回值异常，或市场关闭/股票停牌/持仓不足"
            elif order_result == -1:
                failure_reason = "订单参数错误或系统异常"
            elif order_result is None or order_result == '':
                failure_reason = "QMT系统无响应"

            print(f"   可能原因: {failure_reason}")
            print(f"   建议检查: 1)委托列表确认是否已下单 2)交易时间 3)股票状态 4)持仓数量")

            return {'success': False, 'reason': f'订单提交可能失败: {failure_reason} (返回值: {order_result})'}

    except Exception as e:
        print(f"❌ 卖出订单执行异常: {e}")
        return {'success': False, 'reason': f'执行异常: {e}'}

# ============================================================================
# K线合成模块
# ============================================================================

def convert_cumulative_to_incremental_volume(data_list):
    """
    将累积成交量转换为增量成交量 - 参考6sk线文件的高级处理逻辑

    参数:
        data_list: 包含成交量数据的列表，每个元素应有'volume'字段

    返回:
        list: 转换后的数据列表，成交量已转换为增量值
    """
    if not data_list or len(data_list) == 0:
        return data_list

    converted_data = []
    prev_volume = 0

    # 计算平均成交量用于异常检测
    volumes = [data.get('volume', 0) for data in data_list]
    avg_volume = sum(volumes) / len(volumes) if volumes else 0

    print(f"📊 成交量转换开始: {len(data_list)}个数据点，平均成交量={avg_volume:.0f}")

    for i, data in enumerate(data_list):
        current_volume = float(data.get('volume', 0))

        if i == 0:
            # 第一个数据点处理 - 参考6sk线逻辑
            if prev_volume == 0:
                # 无历史数据，使用保守估算
                incremental_volume = max(1, current_volume / 1000)  # 假设是累计的千分之一作为增量
                print(f"📊 首次数据点，累积成交量: {current_volume:.0f}, 估算增量: {incremental_volume:.0f}")
            else:
                incremental_volume = current_volume
        else:
            # 后续数据点 - 计算增量成交量
            raw_increment = current_volume - prev_volume

            if raw_increment > 0:
                incremental_volume = raw_increment
                print(f"📊 增量成交量: {incremental_volume:.0f} (累积: {current_volume:.0f})")
            elif raw_increment == 0:
                # 成交量没有变化，使用最小增量
                incremental_volume = 1
                print(f"📊 成交量无变化，使用最小增量: {incremental_volume:.0f}")
            else:
                # 负增量，可能是数据重置或异常 - 参考6sk线处理
                incremental_volume = max(1, current_volume / 1000)
                print(f"⚠️ 检测到负增量: {raw_increment:.0f}, 使用保守估算: {incremental_volume:.0f}")

        # 异常值检测和修正 - 参考6sk线逻辑
        if avg_volume > 0 and incremental_volume > avg_volume * 10:
            print(f"🚨 检测到异常巨大的增量成交量: {incremental_volume:.0f} (平均值的{incremental_volume/avg_volume:.1f}倍)")
            original_volume = incremental_volume
            incremental_volume = min(incremental_volume, avg_volume * 3)
            print(f"   🔧 修正为保守值: {incremental_volume:.0f}")

        # 最小值保护
        incremental_volume = max(1, incremental_volume)

        # 创建新的数据副本，更新成交量为增量值
        new_data = data.copy()
        new_data['volume'] = incremental_volume
        new_data['original_volume'] = current_volume  # 保留原始累积成交量
        converted_data.append(new_data)

        prev_volume = current_volume

        print(f"📊 成交量转换[{i+1}]: 累积={current_volume:.0f} → 增量={incremental_volume:.0f}")

    print(f"✅ 成交量转换完成: 处理{len(converted_data)}个数据点")
    return converted_data

def process_volume_data_qmt(ContextInfo, current_volume, stock_code):
    """
    处理QMT累积成交量数据，转换为增量成交量 - 参考6sk线文件实现

    参数:
        ContextInfo: QMT上下文对象
        current_volume: 当前获取的累积成交量数据（来自QMT API）
        stock_code: 股票代码

    返回:
        float: 处理后的增量成交量
    """
    try:
        current_volume = float(current_volume)

        # 初始化成交量处理相关变量
        if not hasattr(ContextInfo, 'volume_processor_initialized'):
            ContextInfo.volume_anomaly_stats = {'count': 0, 'total_corrections': 0}
            ContextInfo.previous_volumes = {}  # 按股票代码存储上次成交量
            ContextInfo.volume_processor_initialized = True
            print("📊 QMT累积成交量转增量处理器初始化完成")

        # 获取该股票的上次成交量
        previous_volume = ContextInfo.previous_volumes.get(stock_code, 0)

        # 计算增量成交量：当前累积成交量 - 上次累积成交量
        if previous_volume == 0:
            # 第一次运行，无法计算增量，使用保守估算
            volume_increment = max(1, current_volume / 1000)  # 假设是累计的千分之一作为增量
            print(f"📊 [{stock_code}] 首次运行，累积成交量: {current_volume:.0f}, 估算增量: {volume_increment:.0f}")
        else:
            # 正常计算增量
            raw_increment = current_volume - previous_volume

            if raw_increment > 0:
                volume_increment = raw_increment
                print(f"📊 [{stock_code}] 增量成交量: {volume_increment:.0f} (累积: {current_volume:.0f})")
            elif raw_increment == 0:
                # 成交量没有变化，使用最小增量
                volume_increment = 1
                print(f"📊 [{stock_code}] 成交量无变化，使用最小增量: {volume_increment:.0f}")
            else:
                # 负增量，可能是数据重置或异常，使用保守估算
                volume_increment = max(1, current_volume / 1000)
                print(f"⚠️ [{stock_code}] 检测到负增量: {raw_increment:.0f}, 使用保守估算: {volume_increment:.0f}")

        # 异常值检测和修正（基于历史平均值）
        if hasattr(ContextInfo, 'volume_history') and stock_code in ContextInfo.volume_history:
            history = ContextInfo.volume_history[stock_code]
            if len(history) > 5:  # 有足够的历史数据
                avg_increment = sum(history) / len(history)
                if avg_increment > 0 and volume_increment > avg_increment * 10:
                    print(f"🚨 [{stock_code}] 检测到异常巨大的增量成交量: {volume_increment:.0f} (平均值的{volume_increment/avg_increment:.1f}倍)")
                    original_volume = volume_increment
                    volume_increment = min(volume_increment, avg_increment * 3)
                    print(f"   🔧 修正为保守值: {volume_increment:.0f}")

                    ContextInfo.volume_anomaly_stats['count'] += 1
                    ContextInfo.volume_anomaly_stats['total_corrections'] += abs(original_volume - volume_increment)

        # 更新历史记录
        if not hasattr(ContextInfo, 'volume_history'):
            ContextInfo.volume_history = {}
        if stock_code not in ContextInfo.volume_history:
            ContextInfo.volume_history[stock_code] = []

        ContextInfo.volume_history[stock_code].append(volume_increment)
        # 保持历史记录在合理范围内
        if len(ContextInfo.volume_history[stock_code]) > 20:
            ContextInfo.volume_history[stock_code] = ContextInfo.volume_history[stock_code][-20:]

        # 更新previous_volume为当前累积成交量
        ContextInfo.previous_volumes[stock_code] = current_volume

        return volume_increment

    except Exception as e:
        print(f"❌ [{stock_code}] 成交量处理失败: {e}")
        return 1  # 返回默认值

def merge_two_bars(data_list, convert_volume=True):
    """
    将两个数据合成为一个OHLC+V K线

    参数:
        data_list: 包含两个数据点的列表
        convert_volume: 是否将累积成交量转换为增量成交量

    返回:
        dict: 合成后的K线数据
    """
    if len(data_list) != 2:
        raise ValueError("需要恰好两个数据进行合成")

    # 如果需要转换成交量，先进行转换
    if convert_volume:
        converted_data = convert_cumulative_to_incremental_volume(data_list)
        data1, data2 = converted_data
    else:
        data1, data2 = data_list

    # 计算OHLC
    open_price = data1['price']
    close_price = data2['price']
    high_price = max(data1['price'], data2['price'])
    low_price = min(data1['price'], data2['price'])

    # 使用增量成交量进行合成
    total_volume = data1['volume'] + data2['volume']

    print(f"📊 K线合成: 成交量 {data1['volume']} + {data2['volume']} = {total_volume}")

    return {
        'start_time': data1.get('time', ''),
        'end_time': data2.get('time', ''),
        'open': round(open_price, 3),
        'high': round(high_price, 3),
        'low': round(low_price, 3),
        'close': round(close_price, 3),
        'volume': total_volume,
        'original_volumes': [data1.get('original_volume', data1['volume']),
                           data2.get('original_volume', data2['volume'])]
    }

def merge_three_bars(data_list, convert_volume=True):
    """
    将三个数据合成为一个OHLC+V K线

    参数:
        data_list: 包含三个数据点的列表
        convert_volume: 是否将累积成交量转换为增量成交量

    返回:
        dict: 合成后的K线数据
    """
    if len(data_list) != 3:
        raise ValueError("需要恰好三个数据进行合成")

    # 如果需要转换成交量，先进行转换
    if convert_volume:
        converted_data = convert_cumulative_to_incremental_volume(data_list)
        data1, data2, data3 = converted_data
    else:
        data1, data2, data3 = data_list

    # 计算OHLC
    open_price = data1['price']
    close_price = data3['price']
    high_price = max(data1['price'], data2['price'], data3['price'])
    low_price = min(data1['price'], data2['price'], data3['price'])

    # 使用增量成交量进行合成
    total_volume = data1['volume'] + data2['volume'] + data3['volume']

    print(f"📊 K线合成: 成交量 {data1['volume']} + {data2['volume']} + {data3['volume']} = {total_volume}")

    return {
        'start_time': data1.get('time', ''),
        'end_time': data3.get('time', ''),
        'open': round(open_price, 3),
        'high': round(high_price, 3),
        'low': round(low_price, 3),
        'close': round(close_price, 3),
        'volume': total_volume,
        'original_volumes': [data1.get('original_volume', data1['volume']),
                           data2.get('original_volume', data2['volume']),
                           data3.get('original_volume', data3['volume'])]
    }

def process_kline_merge(ContextInfo, bar_time, last_price, last_volume, dt_obj):
    """
    处理K线合成逻辑

    参数:
        ContextInfo: QMT上下文对象
        bar_time: 时间字符串
        last_price: 当前价格
        last_volume: 当前成交量
        dt_obj: 时间对象
    """
    if not STRATEGY_CONFIG['enable_kline_merge']:
        return

    # 初始化K线合成相关变量
    if not hasattr(ContextInfo, 'kline_buffer'):
        ContextInfo.kline_buffer = []
        ContextInfo.merged_klines = []
        ContextInfo.bar_count = 0

    # 构建当前数据
    current_data = {
        'time': bar_time,
        'price': last_price,
        'volume': last_volume,
        'datetime': dt_obj
    }

    ContextInfo.kline_buffer.append(current_data)
    ContextInfo.bar_count += 1

    print(f"📈 K线缓冲区: {len(ContextInfo.kline_buffer)}个数据点")

    # 根据配置的合成比例进行合成
    merge_ratio = STRATEGY_CONFIG['merge_ratio']
    if len(ContextInfo.kline_buffer) >= merge_ratio:
        merge_count = 0
        while len(ContextInfo.kline_buffer) >= merge_ratio:
            convert_volume = STRATEGY_CONFIG['convert_cumulative_volume']
            if merge_ratio == 2:
                merged_kline = merge_two_bars(ContextInfo.kline_buffer[:2], convert_volume=convert_volume)
            elif merge_ratio == 3:
                merged_kline = merge_three_bars(ContextInfo.kline_buffer[:3], convert_volume=convert_volume)
            else:
                # 默认使用2根合成
                merged_kline = merge_two_bars(ContextInfo.kline_buffer[:2], convert_volume=convert_volume)
                merge_ratio = 2

            ContextInfo.merged_klines.append(merged_kline)

            print(f"✅ 合成K线{merge_count + 1}: OHLC[{merged_kline['open']}, {merged_kline['high']}, {merged_kline['low']}, {merged_kline['close']}], 成交量={merged_kline['volume']}")

            ContextInfo.kline_buffer = ContextInfo.kline_buffer[merge_ratio:]
            merge_count += 1

        print(f"🔄 本次合成{merge_count}个K线，剩余缓冲区: {len(ContextInfo.kline_buffer)}")

    # 缓冲区异常清理
    max_buffer_size = STRATEGY_CONFIG['max_buffer_size']
    if len(ContextInfo.kline_buffer) > max_buffer_size:
        ContextInfo.kline_buffer = ContextInfo.kline_buffer[-1:]
        print("⚠️ 缓冲区异常增长，已清理")

    # 滑动窗口数据管理
    maintain_sliding_window(ContextInfo)

def maintain_sliding_window(ContextInfo):
    """
    维护滑动窗口数据管理

    参数:
        ContextInfo: QMT上下文对象
    """
    try:
        if not hasattr(ContextInfo, 'merged_klines'):
            return

        # 计算最大K线数量
        MAX_KLINES = STRATEGY_CONFIG['max_history_bars']
        current_count = len(ContextInfo.merged_klines)

        if current_count > MAX_KLINES:
            # FIFO：移除最旧的数据，保留最新的数据
            removed_count = current_count - MAX_KLINES
            ContextInfo.merged_klines = ContextInfo.merged_klines[-MAX_KLINES:]

            print(f"🔄 滑动窗口清理: 移除{removed_count}个旧K线，保留{len(ContextInfo.merged_klines)}个")

            # 更新滑动窗口统计
            if not hasattr(ContextInfo, 'sliding_window_stats'):
                ContextInfo.sliding_window_stats = {'total_removed': 0, 'cleanup_count': 0}

            ContextInfo.sliding_window_stats['total_removed'] += removed_count
            ContextInfo.sliding_window_stats['cleanup_count'] += 1

            # 每10次清理输出一次统计
            if ContextInfo.sliding_window_stats['cleanup_count'] % 10 == 0:
                print(f"📊 滑动窗口统计: 已清理{ContextInfo.sliding_window_stats['cleanup_count']}次，"
                      f"累计移除{ContextInfo.sliding_window_stats['total_removed']}个K线")

        # 内存优化：定期强制垃圾回收
        if hasattr(ContextInfo, 'sliding_window_stats') and ContextInfo.sliding_window_stats['cleanup_count'] % 50 == 0:
            import gc
            gc.collect()
            print("🧹 执行内存垃圾回收")

    except Exception as e:
        print(f"❌ 滑动窗口维护失败: {e}")

def get_merged_kline_data(ContextInfo):
    """
    获取合成K线数据用于技术指标计算

    参数:
        ContextInfo: QMT上下文对象

    返回:
        dict: 包含OHLCV数组的字典
    """
    try:
        if not hasattr(ContextInfo, 'merged_klines') or not ContextInfo.merged_klines:
            return None

        merged_klines = ContextInfo.merged_klines

        return {
            'opens': [kline['open'] for kline in merged_klines],
            'highs': [kline['high'] for kline in merged_klines],
            'lows': [kline['low'] for kline in merged_klines],
            'closes': [kline['close'] for kline in merged_klines],
            'volumes': [kline['volume'] for kline in merged_klines],
            'count': len(merged_klines)
        }

    except Exception as e:
        print(f"❌ 获取合成K线数据失败: {e}")
        return None

# ============================================================================
# 委托单管理模块
# ============================================================================

def manage_pending_orders(ContextInfo, stock_code, current_price):
    """
    管理未成交的委托单 - 实现撤单重新挂单逻辑

    参数:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码
        current_price: 当前价格

    返回:
        dict: 处理结果
    """
    try:
        print(f"\n🔍 检查未成交委托单: {stock_code}")

        # 查询所有未处理的委托
        pending_orders = query_orders(ContextInfo, stock_code, 'ALL')
        pending_orders = [order for order in pending_orders if order['is_pending']]

        if not pending_orders:
            print("✅ 没有未处理的委托单")
            return {'processed': 0, 'cancelled': 0, 'reordered': 0}

        print(f"📋 发现 {len(pending_orders)} 个未处理委托单")

        processed_count = 0
        cancelled_count = 0
        reordered_count = 0

        for order in pending_orders:
            try:
                order_type = order['op_type_desc']
                order_price = order['price']
                order_volume = order['total_volume']
                order_id = order['order_id']

                print(f"🔄 处理委托: {order_type} {order_volume}股@{order_price:.3f}")

                # 判断是否需要撤单重新挂单
                should_cancel = False
                reason = ""

                if order_type == '买入':
                    # 买入委托：未成交单需要撤销（按您的要求）
                    should_cancel = True
                    price_diff_pct = abs(order_price - current_price) / current_price
                    reason = f"买入委托未成交自动撤销 (价格偏离{price_diff_pct:.2%})"

                elif order_type == '卖出':
                    # 卖出委托：撤销后重新挂单（按您的要求）
                    should_cancel = True
                    reason = "卖出委托自动撤单重新挂单"

                if should_cancel:
                    print(f"🔄 撤销委托: {reason}")

                    # 撤销委托
                    cancel_result = cancel_orders(ContextInfo, order_ids=[order_id])
                    if cancel_result['cancel_success'] > 0:
                        cancelled_count += 1
                        print(f"✅ 撤单成功: {order_id}")

                        # 等待撤单完成
                        time.sleep(0.5)

                        # 重新挂单处理
                        if order_type == '买入':
                            # 买入委托：只撤销，不重新挂单（按您的要求）
                            print(f"📤 买入委托已撤销，不重新挂单")

                        elif order_type == '卖出':
                            sell_result = execute_sell_order(ContextInfo, stock_code, current_price,
                                                           volume=order_volume, reason="重新挂单",
                                                           auto_cancel_pending=False)
                            if sell_result['success']:
                                reordered_count += 1
                                print(f"✅ 重新卖出挂单成功")
                            else:
                                print(f"❌ 重新卖出挂单失败: {sell_result['reason']}")
                    else:
                        print(f"❌ 撤单失败: {order_id}")

                processed_count += 1

            except Exception as e:
                print(f"❌ 处理委托异常: {e}")
                continue

        result = {
            'processed': processed_count,
            'cancelled': cancelled_count,
            'reordered': reordered_count
        }

        print(f"📊 委托处理结果: 处理{processed_count}个，撤销{cancelled_count}个，重新挂单{reordered_count}个")
        return result

    except Exception as e:
        print(f"❌ 委托单管理异常: {e}")
        return {'processed': 0, 'cancelled': 0, 'reordered': 0}

# ============================================================================
# 策略状态管理模块
# ============================================================================

def update_strategy_state(ContextInfo, stock_code, current_price):
    """
    更新策略状态 - 跟踪持仓后的最高价和移动止盈价格

    参数:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码
        current_price: 当前价格
    """
    try:
        # 初始化策略状态
        if not hasattr(ContextInfo, 'strategy_states'):
            ContextInfo.strategy_states = {}

        if stock_code not in ContextInfo.strategy_states:
            ContextInfo.strategy_states[stock_code] = {
                'highest_price_since_entry': 0,
                'trailing_stop_price': 0,
                'entry_price': 0,
                'last_update_time': '',
                'bars_since_entry': 0
            }

        state = ContextInfo.strategy_states[stock_code]

        # 获取当前持仓信息
        position = get_current_position_info(ContextInfo, stock_code)

        if position['has_position']:
            # 有持仓，更新状态
            if state['entry_price'] == 0:
                # 新建仓位
                state['entry_price'] = position['avg_price']
                state['highest_price_since_entry'] = current_price
                state['trailing_stop_price'] = 0
                state['bars_since_entry'] = 0
                print(f"📊 新建仓位状态: 入场价={state['entry_price']:.3f}")

            # 更新最高价
            if current_price > state['highest_price_since_entry']:
                state['highest_price_since_entry'] = current_price
                print(f"📈 更新最高价: {current_price:.3f}")

            # 更新K线计数
            state['bars_since_entry'] += 1

        else:
            # 无持仓，重置状态
            if state['entry_price'] > 0:
                print(f"📊 清空仓位状态")
            state['entry_price'] = 0
            state['highest_price_since_entry'] = 0
            state['trailing_stop_price'] = 0
            state['bars_since_entry'] = 0

        state['last_update_time'] = str(datetime.datetime.now())

    except Exception as e:
        print(f"❌ 策略状态更新异常: {e}")

def get_strategy_state(ContextInfo, stock_code):
    """
    获取策略状态

    参数:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码

    返回:
        dict: 策略状态
    """
    try:
        if not hasattr(ContextInfo, 'strategy_states'):
            return {
                'highest_price_since_entry': 0,
                'trailing_stop_price': 0,
                'entry_price': 0,
                'bars_since_entry': 0
            }

        return ContextInfo.strategy_states.get(stock_code, {
            'highest_price_since_entry': 0,
            'trailing_stop_price': 0,
            'entry_price': 0,
            'bars_since_entry': 0
        })

    except Exception as e:
        print(f"❌ 获取策略状态异常: {e}")
        return {
            'highest_price_since_entry': 0,
            'trailing_stop_price': 0,
            'entry_price': 0,
            'bars_since_entry': 0
        }

# ============================================================================
# 工具函数
# ============================================================================

def get_main_stock_code(ContextInfo):
    """
    获取主图标的股票代码

    参数:
        ContextInfo: QMT上下文对象

    返回:
        str: 主图标的股票代码
    """
    try:
        if hasattr(ContextInfo, 'stockcode'):
            return ContextInfo.stockcode
        elif hasattr(ContextInfo, 'stock_code'):
            return ContextInfo.stock_code
        elif hasattr(ContextInfo, 'symbol'):
            return ContextInfo.symbol
        else:
            # 如果无法获取主图标的，返回默认值或从股票池中取第一个
            if hasattr(ContextInfo, 'stock_pool') and ContextInfo.stock_pool:
                return ContextInfo.stock_pool[0]
            return '000001.SZ'  # 默认值
    except Exception as e:
        print(f"❌ 获取主图标的异常: {e}")
        return '000001.SZ'

def get_main_account_id(ContextInfo):
    """
    获取主图账户ID

    参数:
        ContextInfo: QMT上下文对象

    返回:
        str: 账户ID
    """
    try:
        # 优先级顺序：accountid > account全局变量 > 默认值
        if hasattr(ContextInfo, 'accountid') and ContextInfo.accountid:
            print(f"📊 使用主图账户ID: {ContextInfo.accountid}")
            return ContextInfo.accountid
        elif 'account' in globals():
            account_id = globals()['account']
            print(f"📊 使用全局账户ID: {account_id}")
            return account_id
        else:
            print(f"📊 使用默认账户ID: *********")
            return '*********'  # 默认账户ID
    except Exception as e:
        print(f"❌ 获取主图账户ID异常: {e}")
        return '*********'

def get_current_position_info(C, stock_code):
    """
    获取当前股票的持仓信息 - 参考交易实时主推示例

    参数:
        C: QMT上下文对象
        stock_code: 股票代码

    返回:
        dict: 持仓信息
    """
    try:
        # 使用正确的账户ID属性
        account_id = getattr(C, 'acct', 'test_account')
        position_info = get_trade_detail_data(account_id, 'stock', 'position')

        if not position_info:
            return {'has_position': False, 'volume': 0, 'avg_price': 0}

        for pos in position_info:
            # 参考示例中的属性访问方式
            pos_stock = getattr(pos, 'm_strInstrumentID', '') + '.' + getattr(pos, 'm_strExchangeID', '')
            if pos_stock == stock_code:
                volume = getattr(pos, 'm_nVolume', 0)
                avg_price = getattr(pos, 'm_dOpenPrice', 0)  # 使用成本价
                position_cost = getattr(pos, 'm_dPositionCost', 0)
                position_profit = getattr(pos, 'm_dPositionProfit', 0)

                return {
                    'has_position': volume > 0,
                    'volume': volume,
                    'avg_price': avg_price,
                    'position_cost': position_cost,
                    'position_profit': position_profit,
                    'market_value': getattr(pos, 'm_dInstrumentValue', 0)
                }

        return {'has_position': False, 'volume': 0, 'avg_price': 0}

    except Exception as e:
        print(f"❌ 获取持仓信息异常: {e}")
        return {'has_position': False, 'volume': 0, 'avg_price': 0}

def print_strategy_status(ContextInfo, stock_code, current_price, entry_price=0, highest_price=0, trailing_stop=0):
    """
    打印策略状态信息

    参数:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码
        current_price: 当前价格
        entry_price: 入场价格
        highest_price: 最高价
        trailing_stop: 移动止盈价格
    """
    print(f"\n📊 策略状态 - {stock_code}")
    print("=" * 50)

    # 持仓信息
    position = get_current_position_info(ContextInfo, stock_code)
    print(f"💼 持仓状态: {'有持仓' if position['has_position'] else '空仓'}")
    if position['has_position']:
        print(f"   持仓数量: {position['volume']}股")
        print(f"   持仓成本: {position['avg_price']:.3f}")
        print(f"   市值: {position['market_value']:.2f}")

    # 价格信息
    print(f"💰 价格信息:")
    print(f"   当前价格: {current_price:.3f}")
    if entry_price > 0:
        profit_pct = (current_price - entry_price) / entry_price * 100
        print(f"   入场价格: {entry_price:.3f}")
        print(f"   盈亏比例: {profit_pct:+.2f}%")
    if highest_price > 0:
        print(f"   最高价格: {highest_price:.3f}")
    if trailing_stop > 0:
        print(f"   移动止盈: {trailing_stop:.3f}")

    # 未处理订单
    if check_pending_orders(ContextInfo, stock_code):
        orders = query_orders(ContextInfo, stock_code)
        pending_orders = [o for o in orders if o['is_pending']]
        print(f"⚠️ 未处理订单: {len(pending_orders)}个")

# ============================================================================
# 使用示例
# ============================================================================

def example_trading_workflow(ContextInfo, stock_code=None, market_data=None):
    """
    完整交易流程示例 - 支持主图标的和K线合成

    参数:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码，如果为None则使用主图标的
        market_data: 市场数据 {'highs': [], 'lows': [], 'closes': []} (可选，优先使用合成K线数据)
    """
    # 获取股票代码
    if stock_code is None and STRATEGY_CONFIG['use_main_stock']:
        stock_code = get_main_stock_code(ContextInfo)
    elif stock_code is None:
        stock_code = '000001.SZ'  # 默认值

    print(f"\n🚀 开始交易流程示例: {stock_code}")

    # 1. 优先使用合成K线数据
    merged_data = get_merged_kline_data(ContextInfo)
    if merged_data and merged_data['count'] > 0:
        market_data = {
            'highs': merged_data['highs'],
            'lows': merged_data['lows'],
            'closes': merged_data['closes'],
            'opens': merged_data['opens'],
            'volumes': merged_data['volumes']
        }
        current_price = merged_data['closes'][-1]
        print(f"📊 使用合成K线数据: {merged_data['count']}根K线")
    elif market_data and 'closes' in market_data and market_data['closes']:
        current_price = market_data['closes'][-1]
        print(f"📊 使用传入的市场数据")
    else:
        # 尝试获取实时市场数据
        real_market_data = get_safe_market_data_for_trading(ContextInfo)
        if real_market_data:
            current_price, _ = real_market_data
            print(f"📊 使用实时市场数据: {current_price:.3f}")
        else:
            print(f"❌ 无法获取任何市场数据，跳过交易流程")
            return
        market_data = None

    # 2. 检查当前持仓
    position = get_current_position_info(ContextInfo, stock_code)

    if not position['has_position']:
        # 无持仓，考虑买入
        print("📈 无持仓，准备买入")

        # 检查买入信号 - 接收三次数据后买入
        buy_signal_result = check_buy_signal_three_bars(ContextInfo, stock_code, market_data)

        print(f"📊 买入信号检查: {buy_signal_result['reason']}")
        if 'data_count' in buy_signal_result:
            print(f"   数据计数: {buy_signal_result['data_count']}")

        if buy_signal_result['buy_signal']:
            buy_result = execute_buy_order(ContextInfo, stock_code, current_price)
            if buy_result['success']:
                print(f"✅ 买入成功: {buy_result}")
            else:
                print(f"❌ 买入失败: {buy_result['reason']}")

    else:
        # 有持仓，检查止盈止损
        print("📊 有持仓，检查唐奇安移动止盈止损")

        entry_price = position['avg_price']
        # 这里需要从策略状态中获取最高价和移动止盈价格
        highest_price = current_price  # 示例
        trailing_stop = 0  # 示例

        exit_result = check_exit_conditions_donchian(
            ContextInfo, current_price, entry_price,
            highest_price, trailing_stop, market_data
        )

        if exit_result['should_exit']:
            print(f"🎯 触发平仓信号: {exit_result['reason']}")
            # 显示动态触发信息
            if 'dynamic_trigger_info' in exit_result:
                trigger_info = exit_result['dynamic_trigger_info']
                print(f"   📊 动态触发阈值: {trigger_info.get('trigger_threshold', 0):.1f}% (波动率: {trigger_info.get('volatility_level', 'unknown')})")

            sell_result = execute_sell_order(ContextInfo, stock_code, current_price, reason=exit_result['reason'])
            if sell_result['success']:
                print(f"✅ 卖出成功: {sell_result}")
            else:
                print(f"❌ 卖出失败: {sell_result['reason']}")
        else:
            print(f"📈 继续持仓: {exit_result['reason']}")

    # 3. 打印策略状态
    print_strategy_status(ContextInfo, stock_code, current_price)

# ============================================================================
# 数据获取模块
# ============================================================================

def get_safe_market_data_for_trading(C):
    """
    安全获取市场数据 - 使用正确的QMT API方法

    参数:
        C: 策略上下文

    返回:
        tuple: (价格, 成交量) 或 None
    """
    try:
        # 使用QMT标准API获取数据（与6sk线文件一致的方法）
        local_data = C.get_market_data_ex(
            ['lastPrice', 'volume'],
            [C.stock],
            period=C.period,
            count=1,
            subscribe=False
        )

        # 数据有效性检查
        if not local_data or C.stock not in local_data:
            print(f"⚠️ 无法获取{C.stock}的市场数据")
            return None

        stock_data = local_data[C.stock]

        # 修复pandas Series布尔值判断错误 - 使用安全的数据检查方法
        def is_data_valid(data):
            """检查数据是否有效（支持pandas Series、numpy数组、列表等）"""
            if data is None:
                return False
            # 检查pandas Series
            if hasattr(data, 'empty'):
                return not data.empty
            # 检查numpy数组或列表
            if hasattr(data, '__len__'):
                return len(data) > 0
            return True

        # 检查lastPrice数据
        last_price_data = stock_data.get('lastPrice')
        if not is_data_valid(last_price_data):
            print(f"⚠️ lastPrice数据无效")
            return None

        # 检查volume数据
        volume_data = stock_data.get('volume')
        if not is_data_valid(volume_data):
            print(f"⚠️ volume数据无效")
            return None

        # 安全提取数据
        try:
            last_price = round(float(last_price_data[0]), 3)
            volume = float(volume_data[0])
        except (IndexError, TypeError, ValueError) as e:
            print(f"⚠️ 数据提取失败: {e}")
            return None

        # 价格合理性检查
        if last_price <= 0:
            print(f"⚠️ 异常价格: {last_price}")
            return None

        # 处理成交量数据 - 使用6sk线文件的处理逻辑
        if STRATEGY_CONFIG.get('convert_cumulative_volume', True):
            # 将累积成交量转换为增量成交量
            processed_volume = process_volume_data_qmt(C, volume, C.stock)
            print(f"📊 市场数据获取成功: 价格={last_price}, 原始成交量={volume}, 增量成交量={processed_volume}")
            return (last_price, processed_volume)
        else:
            print(f"📊 市场数据获取成功: 价格={last_price}, 成交量={volume} (未转换)")
            return (last_price, volume)

    except Exception as e:
        print(f"❌ 数据获取失败: {e}")
        return None

# ============================================================================
# QMT标准框架函数
# ============================================================================

def init(C):
    """
    QMT策略初始化函数 - 集成数据缓冲区功能
    """
    print("🚀 止盈止损下单模块初始化 - 数据缓冲区增强版")
    print("="*60)

    # 基础设置
    C.stock = C.stockcode + '.' + C.market
    print(f"📊 交易标的: {C.stock}")
    print(f"🕐 初始化时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 账户信息 - 可以获取主图ID
    try:
        # 使用专门的函数获取主图账户ID
        C.acct = get_main_account_id(C)
        C.acct_type = globals().get('accountType', 'STOCK')

        # 设置账户
        if hasattr(C, 'set_account'):
            C.set_account(C.acct)
            print(f"✅ 账户设置成功: {C.acct}")
        else:
            print(f"📊 账户ID: {C.acct} (无set_account方法)")

    except Exception as e:
        print(f"⚠️ 账户设置异常: {e}")
        C.acct = "*********"  # 默认账户ID
        C.acct_type = "STOCK"

    # 数据缓冲区功能（可选）- 在init中完成所有初始化
    if STRATEGY_CONFIG.get('enable_data_buffer', False):
        print(f"\n📥 数据缓冲区功能已启用，开始初始化...")

        try:
            # 创建数据缓冲区
            buffer_size = STRATEGY_CONFIG.get('buffer_size', 100)
            C.data_buffer = MarketDataBuffer(buffer_size)
            print(f"📊 数据缓冲区初始化完成，容量: {buffer_size}根K线(基于tick合成)")

            # 直接获取历史tick数据填充缓冲区（不需要下载）
            print("📥 获取历史tick数据填充缓冲区...")
            history_count = STRATEGY_CONFIG.get('history_data_count', 60)

            success = C.data_buffer.preload_history_data(
                C, C.stock, 'tick', history_count
            )

            if success:
                print(f"✅ 历史tick数据预加载成功，缓冲区包含 {C.data_buffer.get_data_count()} 根K线")
            else:
                print("⚠️ 历史tick数据预加载失败，将使用实时tick数据逐步填充")

            # 标记缓冲区已初始化
            C.buffer_initialized = True

        except Exception as e:
            print(f"⚠️ 数据缓冲区初始化异常: {e}")
            import traceback
            traceback.print_exc()
            # 即使失败也标记为已初始化，避免重复尝试
            C.buffer_initialized = True
    else:
        print(f"\n📊 数据缓冲区功能已禁用，使用传统模式")

    # 初始化策略状态
    C.strategy_state = {}
    C.data_count = 0
    C.buy_signal_triggered = False

    # 数据缓冲区相关状态（不要覆盖已设置的buffer_initialized）
    if not hasattr(C, 'buffer_initialized'):
        C.buffer_initialized = False  # 只在未设置时才设置为False
    C.debug_mode = STRATEGY_CONFIG.get('debug_mode', False)

    print(f"\n💡 策略初始化完成，等待handlebar进行数据获取...")
    print("="*60)

def handlebar(C):
    """
    QMT策略主函数 - 集成数据缓冲区的完整买入卖出逻辑
    """
    try:
        # 🔒 首先进行安全检查 - 只在最新K线执行逻辑
        if not C.is_last_bar():
            return

        # 数据缓冲区功能（可选）- 只处理实时数据更新
        if STRATEGY_CONFIG.get('enable_data_buffer', False):
            # 检查缓冲区是否已在init中初始化
            if not getattr(C, 'buffer_initialized', False):
                print("⚠️ 数据缓冲区未在init中初始化，跳过本次执行")
                return

            # 更新数据缓冲区（只处理实时数据）
            update_buffer_with_current_data_qmt(C)

            # 检查是否有足够数据进行交易
            if hasattr(C, 'data_buffer'):
                min_periods = STRATEGY_CONFIG.get('min_trading_periods', 20)
                if not C.data_buffer.is_ready_for_trading(min_periods):
                    if getattr(C, 'debug_mode', False):
                        print(f"⏳ 数据不足，需要{min_periods}根K线，当前{C.data_buffer.get_data_count()}根")
                    return

        # 获取当前价格和成交量
        current_price = None
        volume = 0

        if STRATEGY_CONFIG.get('enable_data_buffer', False) and hasattr(C, 'data_buffer'):
            # 使用缓冲区数据
            current_price = C.data_buffer.get_current_price()
            volume = C.data_buffer.get_current_volume()

            if current_price is None or current_price <= 0:
                print("⚠️ 缓冲区无有效价格数据，跳过本次执行")
                return
        else:
            # 使用原有方法获取市场数据
            market_data = get_safe_market_data_for_trading(C)
            if not market_data:
                print("⚠️ 无法获取市场数据，跳过本次执行")
                return
            current_price, volume = market_data

        stock_code = C.stock
        print(f"\n📊 策略执行 - {stock_code}")
        print(f"📊 当前价格: {current_price:.3f}, 成交量: {volume}")

        # 1. 管理未成交的委托单（按您的要求：买入委托撤销，卖出委托撤销后重新挂单）
        manage_result = manage_pending_orders(C, stock_code, current_price)
        if manage_result['cancelled'] > 0:
            print(f"🔄 委托管理: 撤销{manage_result['cancelled']}个，重新挂单{manage_result['reordered']}个")

        # 2. 更新策略状态
        update_strategy_state(C, stock_code, current_price)

        # 3. 获取持仓信息和策略状态
        position = get_current_position_info(C, stock_code)
        strategy_state = get_strategy_state(C, stock_code)

        # 4. 构建市场数据（用于技术指标计算）
        market_data_dict = {
            'highs': [current_price] * 20,
            'lows': [current_price] * 20,
            'closes': [current_price] * 20
        }

        if not position['has_position']:
            # 5A. 无持仓 - 检查买入信号
            print("📈 无持仓状态，检查买入信号")

            buy_signal_result = check_buy_signal_three_bars(C, stock_code, market_data_dict)
            print(f"📊 买入信号检查: {buy_signal_result['reason']}")

            if 'data_count' in buy_signal_result:
                print(f"   数据计数: {buy_signal_result['data_count']}")

            if buy_signal_result['buy_signal']:
                print("🎯 买入信号触发，执行买入")
                buy_result = execute_buy_order(C, stock_code, current_price)
                if buy_result['success']:
                    print(f"✅ 买入订单提交成功: {buy_result['volume']}股@{buy_result['price']:.3f}")
                    print(f"   订单ID: {buy_result['order_id']}")
                else:
                    print(f"❌ 买入订单失败: {buy_result['reason']}")
            else:
                print("⏳ 买入信号未触发，继续等待")

        else:
            # 5B. 有持仓 - 检查止盈止损
            print(f"📊 持仓状态: {position['volume']}股@{position['avg_price']:.3f}")
            print(f"   持仓成本: {position['avg_price']:.3f}")
            print(f"   当前市值: {position['market_value']:.2f}")
            print(f"   浮动盈亏: {position['position_profit']:.2f}")

            # 使用策略状态中的数据进行止盈止损检查
            entry_price = strategy_state['entry_price'] or position['avg_price']
            highest_price = strategy_state['highest_price_since_entry'] or current_price
            trailing_stop = strategy_state['trailing_stop_price']

            print(f"📊 策略状态: 入场价={entry_price:.3f}, 最高价={highest_price:.3f}, 移动止盈={trailing_stop:.3f}")

            # 执行止盈止损检查
            exit_result = check_exit_conditions_donchian(
                C, current_price, entry_price, highest_price, trailing_stop, market_data_dict
            )

            print(f"🔍 止盈止损检查: {exit_result['reason']}")

            if exit_result['should_exit']:
                print(f"🎯 平仓信号触发: {exit_result['reason']}")

                # 显示详细的平仓信息
                if 'atr_info' in exit_result:
                    atr_info = exit_result['atr_info']
                    print(f"   ATR信息: {atr_info.get('atr', 0):.3f}")

                if 'donchian_info' in exit_result:
                    donchian_info = exit_result['donchian_info']
                    print(f"   唐奇安通道: 上轨={donchian_info.get('upper_channel', 0):.3f}, 下轨={donchian_info.get('lower_channel', 0):.3f}")

                # 执行卖出
                sell_result = execute_sell_order(C, stock_code, current_price, reason=exit_result['reason'])
                if sell_result['success']:
                    print(f"✅ 卖出订单提交成功: {sell_result['volume']}股@{sell_result['price']:.3f}")
                    print(f"   订单ID: {sell_result['order_id']}")
                    print(f"   卖出原因: {sell_result['reason']}")
                else:
                    print(f"❌ 卖出订单失败: {sell_result['reason']}")

                # 更新策略状态中的移动止盈价格
                if 'new_trailing_stop' in exit_result:
                    C.strategy_states[stock_code]['trailing_stop_price'] = exit_result['new_trailing_stop']

            else:
                print(f"📈 继续持仓: {exit_result['reason']}")

                # 更新策略状态
                if 'new_trailing_stop' in exit_result:
                    C.strategy_states[stock_code]['trailing_stop_price'] = exit_result['new_trailing_stop']
                if 'new_highest_price' in exit_result:
                    C.strategy_states[stock_code]['highest_price_since_entry'] = exit_result['new_highest_price']

        # 6. 打印策略状态摘要
        print_strategy_status(C, stock_code, current_price,
                            strategy_state['entry_price'],
                            strategy_state['highest_price_since_entry'],
                            strategy_state['trailing_stop_price'])

    except Exception as e:
        print(f"❌ 策略执行异常: {e}")
        import traceback
        traceback.print_exc()

# ============================================================================
# 动态触发阈值配置示例
# ============================================================================

def show_atr_config_details():
    """
    展示ATR触发阈值和止损配置详解
    """
    print("\n" + "="*60)
    print("🎯 ATR触发阈值和止损配置详解")
    print("="*60)

    # 当前配置
    atr_period = STRATEGY_CONFIG['atr_period']
    atr_stop_loss_multiplier = STRATEGY_CONFIG['atr_stop_loss_multiplier']
    atr_trigger_multiplier = STRATEGY_CONFIG['atr_trigger_multiplier']

    print(f"\n📊 当前ATR配置:")
    print(f"   ATR计算周期: {atr_period}根K线")
    print(f"   ATR止损倍数: {atr_stop_loss_multiplier}倍")
    print(f"   ATR移动止盈触发倍数: {atr_trigger_multiplier}倍")

    market_scenarios = [
        {"name": "低波动市场", "atr": 0.15, "description": "震荡整理期", "price": 130.0},
        {"name": "中等波动市场", "atr": 0.25, "description": "正常交易期", "price": 130.0},
        {"name": "高波动市场", "atr": 0.35, "description": "趋势突破期", "price": 130.0},
        {"name": "极端波动市场", "atr": 0.50, "description": "重大事件期", "price": 130.0}
    ]

    print(f"\n🎯 不同市场环境下的阈值计算 (假设入场价130.0):")
    print("-" * 80)

    for scenario in market_scenarios:
        atr = scenario['atr']
        price = scenario['price']

        # 计算各种阈值
        trigger_threshold = atr * atr_trigger_multiplier
        stop_loss_threshold = atr * atr_stop_loss_multiplier
        stop_loss_price = price - stop_loss_threshold
        trigger_price = price + trigger_threshold

        trigger_percentage = (trigger_threshold / price) * 100
        stop_loss_percentage = (stop_loss_threshold / price) * 100

        print(f"\n🎯 {scenario['name']} ({scenario['description']})")
        print(f"   ATR值: {atr:.3f} ({atr/price*100:.2f}%)")
        print(f"   📈 移动止盈触发: {trigger_threshold:.3f} ({trigger_percentage:.2f}%) → 需涨到{trigger_price:.3f}")
        print(f"   🛡️ ATR止损触发: {stop_loss_threshold:.3f} ({stop_loss_percentage:.2f}%) → 跌破{stop_loss_price:.3f}")

    print(f"\n💡 配置说明:")
    print(f"   🔸 移动止盈触发: 只有利润达到ATR×{atr_trigger_multiplier}倍时才开始跟踪移动止盈")
    print(f"   🔸 ATR动态止损: 当价格跌破入场价-ATR×{atr_stop_loss_multiplier}倍时触发止损")
    print(f"   🔸 数据不足保护: ATR=0时使用1%最小触发阈值，0.5%固定止损")

    print(f"\n⚙️ 配置建议:")
    print("   - 保守型: atr_trigger_multiplier = 2.0, atr_stop_loss_multiplier = 1.5")
    print("   - 平衡型: atr_trigger_multiplier = 3.0, atr_stop_loss_multiplier = 2.0 (当前)")
    print("   - 激进型: atr_trigger_multiplier = 4.0, atr_stop_loss_multiplier = 2.5")

def show_account_id_config():
    """
    展示账户ID获取配置示例
    """
    print("\n📊 账户ID获取配置示例")
    print("="*50)

    print("🎯 获取优先级顺序:")
    print("   1. 主图账户ID: C.accountid (最高优先级)")
    print("   2. 全局账户变量: globals()['account']")
    print("   3. 默认账户: '*********' (示例账户)")

    print("\n💡 使用场景:")
    print("   📈 主图模式: 自动使用当前图表的账户ID")
    print("   🔧 策略模式: 使用全局设置的账户ID")
    print("   🧪 测试模式: 使用默认示例账户ID")

    print("\n⚙️ 配置方法:")
    print("   # 方法1: 在QMT中打开图表时自动获取")
    print("   # 方法2: 设置全局变量 account = '你的账户ID'")
    print("   # 方法3: 使用默认测试账户")

def show_kline_merge_config():
    """
    展示K线合成和交易单位配置示例
    """
    print("\n📈 K线合成和交易配置示例")
    print("="*50)

    print("🔄 K线合成配置:")
    print(f"   启用K线合成: {STRATEGY_CONFIG['enable_kline_merge']}")
    print(f"   合成比例: {STRATEGY_CONFIG['merge_ratio']}根合成1根")
    print(f"   成交量转换: {STRATEGY_CONFIG['convert_cumulative_volume']} (累积→增量)")

    print("\n📊 交易单位配置:")
    print("   最小交易单位: 10股 (1手)")
    print("   买入数量计算: 按10股整数倍")

    print("\n💡 成交量处理说明:")
    print("   🔸 累积成交量: QMT API返回的原始成交量数据")
    print("   🔸 增量成交量: 当前K线相对于前一根K线的新增成交量")
    print("   🔸 合成逻辑: 将增量成交量相加得到合成K线的总成交量")
    print("   🔸 智能处理: 参考6sk线文件的高级成交量处理逻辑")
    print("   🔸 异常检测: 自动检测和修正异常成交量数据")
    print("   🔸 历史跟踪: 维护成交量历史记录用于异常值检测")

    print("\n⚙️ 配置调整:")
    print("   # 关闭成交量转换（如果API已返回增量成交量）")
    print("   STRATEGY_CONFIG['convert_cumulative_volume'] = False")
    print("   # 调整合成比例")
    print("   STRATEGY_CONFIG['merge_ratio'] = 3  # 3根合成1根")
    print("   # 调整买入信号数据次数要求")
    print("   STRATEGY_CONFIG['buy_signal_count_required'] = 5  # 需要5次数据")

def show_buy_signal_config():
    """
    展示买入信号配置示例
    """
    print("\n🎯 买入信号配置示例")
    print("="*50)

    print("📊 买入条件:")
    print("   ✅ 数据接收次数: 连续接收指定次数的K线数据")
    print("   ✅ 无持仓状态: 只有在无持仓时才检查买入信号")
    print("   ✅ 信号唯一性: 每次只触发一次买入信号")
    print("   ❌ 技术指标检查: 已移除，无需价格趋势确认")

    print("\n⚙️ 配置参数:")
    print("   STRATEGY_CONFIG['buy_signal_count_required'] = 3  # 需要连续接收的数据次数")
    print("   STRATEGY_CONFIG['buy_signal_enabled'] = True      # 是否启用买入信号检查")

    print("\n📈 执行流程:")
    print("   1️⃣ 策略启动，开始接收K线数据")
    print("   2️⃣ 每次接收数据时计数器+1")
    print("   3️⃣ 达到要求次数时立即触发买入信号")
    print("   4️⃣ 执行买入订单")
    print("   5️⃣ 买入成功后进入持仓监控状态")

    print("\n💡 优势:")
    print("   🚀 简单直接: 无复杂技术指标判断")
    print("   ⚡ 快速响应: 达到数据次数立即买入")
    print("   🎯 策略纯净: 避免过度优化和曲线拟合")
    print("   🔒 风险可控: 明确的买入时机，避免主观判断")

def show_order_management_config():
    """
    展示委托单管理配置示例
    """
    print("\n📋 委托单管理配置示例")
    print("="*50)

    print("🔄 委托单自动管理:")
    print("   📤 买入委托: 未成交单自动撤销（不重新挂单）")
    print("   📤 卖出委托: 自动撤销后重新挂单（确保及时成交）")
    print("   ⏰ 检查频率: 每个K线周期执行一次")

    print("\n💡 管理逻辑:")
    print("   🎯 买入策略: 未成交委托单直接撤销，避免追高风险")
    print("   🎯 卖出策略: 主动撤单重挂，提高成交概率")
    print("   🎯 风险控制: 防止委托单长时间挂单不成交")

    print("\n📊 执行流程:")
    print("   1️⃣ 查询未成交委托单")
    print("   2️⃣ 判断是否需要撤单")
    print("   3️⃣ 执行撤单操作")
    print("   4️⃣ 重新计算挂单价格")
    print("   5️⃣ 提交新的委托单")

    print("\n⚙️ 相关配置:")
    print("   buy_hang_offset_ratio: 买入挂单偏移比例")
    print("     正值: 低于市价买入 (保守策略, 如 0.0002 = 低0.02%)")
    print("     负值: 高于市价买入 (激进策略, 如 -0.0002 = 高0.02%)")
    print("   sell_hang_offset_ratio: 卖出挂单偏移比例")
    print("     正值: 高于市价卖出 (保守策略, 如 0.0002 = 高0.02%)")
    print("     负值: 低于市价卖出 (激进策略, 如 -0.0002 = 低0.02%)")
    print("   价格偏离阈值: 1% (可调整)")

# ============================================================================
# 模块信息
# ============================================================================

__version__ = "3.0.0"
__author__ = "QMT止盈止损下单模块"
__description__ = """
QMT止盈止损和下单模块 - 数据缓冲区增强版本

主要功能：
1. 基于ATR的动态止盈止损系统
2. 唐奇安通道移动止盈（无偏移）
3. K线合成功能（支持增量成交量计算）
4. 带挂单偏移的买入/卖出订单执行
5. 完整的委托查询和撤单功能
6. 主图标的和账户ID自动识别
7. 标准QMT框架兼容
8. 智能委托单管理系统
9. 【新增】历史数据缓冲区管理（方案三优化）
10. 【新增】实时技术指标计算（MA、ATR等）
11. 【新增】开盘即可交易（预加载历史数据）
12. 【新增】高效内存管理（滑动窗口机制）

核心功能：
✅ 完整的买入后卖出逻辑 (买入→持仓监控→止盈止损→卖出)
✅ 智能委托单管理 (自动撤单重新挂单)
✅ 策略状态持久化 (最高价跟踪、移动止盈价格)
✅ 主图账户ID自动获取 (C.accountid优先级最高)
✅ 多级账户ID获取机制 (主图→全局→默认)
✅ 智能成交量转换 (累积成交量→增量成交量)
✅ 10股交易单位支持 (1手=10股)
✅ 委托查询异常修复 (accID→acct)

委托单管理：
🔄 买入委托: 未成交单自动撤销（不重新挂单，避免追高）
🔄 卖出委托: 自动撤销后重新挂单（提高成交概率）
🔄 实时监控: 每个K线周期检查未成交委托单
🔄 风险控制: 防止委托单长时间挂单不成交

技术改进：
🔧 K线合成支持累积成交量自动转换为增量成交量
🔧 参考6sk线文件的高级成交量处理逻辑（异常检测、历史跟踪）
🔧 交易单位从100股调整为10股（适配新的交易规则）
🔧 修复委托查询中的属性名错误问题
🔧 修复datetime.now()导入问题
🔧 简化买入信号逻辑（移除技术指标检查）
🔧 修复passorder参数类型错误（参考6sk线调用方式）
🔧 增强ATR数据不足时的保护机制（使用估算值和固定阈值）
🔧 添加订单失败的详细诊断信息
🔧 修复唐奇安移动止盈触发阈值为0的问题（避免任何微小利润都触发）
🔧 完整的策略状态跟踪和持久化

使用方法：
1. 在QMT中导入此模块
2. 调用init()进行初始化
3. 策略将自动执行完整的买入→持仓→卖出流程
4. 委托单将被智能管理，确保及时成交
"""

def show_buffer_usage_guide():
    """显示数据缓冲区使用指南"""
    print("📊 数据缓冲区使用指南 (Tick数据专用版 - QMT标准模式)")
    print("="*60)
    print("🚀 策略初始化 (init函数) - 优化版:")
    print("   def init(ContextInfo):")
    print("       # 在init中完成所有数据缓冲区初始化和历史数据获取")
    print("       ContextInfo.data_buffer = MarketDataBuffer(buffer_size)")
    print("       ContextInfo.data_buffer.preload_history_data(...)")
    print("       ContextInfo.buffer_initialized = True")
    print("")
    print("📈 Tick数据处理 (handlebar函数) - 优化版:")
    print("   def handlebar(ContextInfo):")
    print("       # 只处理实时数据更新，不再进行历史数据获取")
    print("       update_buffer_with_current_data_qmt(ContextInfo)")
    print("       # 执行交易逻辑")
    print("")
    print("⚙️ Tick数据配置参数调整:")
    print("   STRATEGY_CONFIG['buffer_size'] = 100          # tick缓冲区大小（优化后）")
    print("   STRATEGY_CONFIG['history_data_count'] = 60    # 历史tick数据量（30根K线）")
    print("   STRATEGY_CONFIG['min_trading_periods'] = 20   # 最少tick周期（10根K线）")
    print("")
    print("🔧 调试模式:")
    print("   ContextInfo.debug_mode = True  # 启用详细日志")
    print("")
    print("📊 Tick数据处理特点:")
    print("   ✅ QMT tick周期：3秒一个tick")
    print("   ✅ 支持字段：lastPrice（真实价格）、volume（增量成交量）、time（时间戳）")
    print("   ❌ 不支持字段：close、open、high、low（会导致ERROR错误）")
    print("   ✅ 自动合成K线：2个tick合成1根K线（6秒K线）")
    print("   ✅ OHLC计算：基于tick价格合成真实K线")
    print("   ✅ 成交量合并：增量成交量相加")
    print("   ✅ 指标计算：基于合成K线计算MA等技术指标")
    print("   ✅ 适合高频交易策略")
    print("")
    print("📊 主要优势:")
    print("   ✅ 开盘即可交易（预加载历史tick数据）")
    print("   ✅ 实时tick指标计算")
    print("   ✅ 高效内存管理（滑动窗口机制）")
    print("   ✅ 支持正负偏移调整")
    print("   ✅ 完整的错误处理和恢复机制")
    print("   ✅ QMT标准模式兼容（init下载，handlebar获取）")
    print("")
    print("💡 重要提示:")
    print("   - 专门针对tick周期策略优化")
    print("   - QMT运行模式：init()下载tick数据，handlebar()获取tick数据")
    print("   - 缓冲区在第一次handlebar调用时初始化")
    print("   - 历史tick数据预加载确保开盘即可交易")
    print("   - tick数据量大，建议增大缓冲区和历史数据量")
    print("="*60)

def show_module_status():
    """显示模块当前状态和功能"""
    print("📊 QMT止盈止损下单模块状态 - Tick数据专用版")
    print("="*60)

    # 检查数据缓冲区功能状态
    buffer_enabled = STRATEGY_CONFIG.get('enable_data_buffer', False)
    print(f"🔧 Tick数据缓冲区功能: {'✅ 已启用' if buffer_enabled else '❌ 已禁用'}")

    if buffer_enabled:
        print(f"📊 Tick缓冲区大小: {STRATEGY_CONFIG.get('buffer_size', 500)}个tick")
        print(f"📥 历史tick数据量: {STRATEGY_CONFIG.get('history_data_count', 200)}个tick")
        print(f"⏳ 最少交易周期: {STRATEGY_CONFIG.get('min_trading_periods', 50)}个tick")
        print(f"🔍 调试模式: {'✅ 已启用' if STRATEGY_CONFIG.get('debug_mode', False) else '❌ 已禁用'}")

    print(f"\n💰 交易配置:")
    print(f"   买入挂单偏移: {STRATEGY_CONFIG.get('buy_hang_offset_ratio', 0.002)*100:.2f}%")
    print(f"   卖出挂单偏移: {STRATEGY_CONFIG.get('sell_hang_offset_ratio', 0.002)*100:.2f}%")
    print(f"   止盈比例: {STRATEGY_CONFIG.get('take_profit_ratio', 0.03)*100:.1f}%")
    print(f"   止损比例: {STRATEGY_CONFIG.get('stop_loss_ratio', 0.02)*100:.1f}%")

    print(f"\n🚀 Tick数据特色功能:")
    print(f"   ✅ 专门针对tick周期优化")
    print(f"   ✅ 支持正负偏移调整")
    print(f"   ✅ 完整的止盈止损逻辑")
    print(f"   ✅ QMT标准模式兼容")
    print(f"   ✅ 实时tick指标计算" if buffer_enabled else "   ⚠️ 传统指标计算模式")
    print(f"   ✅ 开盘即可交易" if buffer_enabled else "   ⚠️ 需等待数据积累")
    print(f"   ✅ 高频交易支持")

    print(f"\n📊 Tick数据特点:")
    print(f"   • 只有lastPrice，没有OHLC")
    print(f"   • 成交量是增量成交量")
    print(f"   • 数据频率高，适合高频策略")
    print(f"   • 需要更大的缓冲区")

    print(f"\n📖 使用方法:")
    if buffer_enabled:
        print(f"   1. 直接在QMT的tick周期中运行此模块")
        print(f"   2. 策略会自动下载历史tick数据")
        print(f"   3. 开盘第一个tick即可开始交易")
        print(f"   4. 可通过修改STRATEGY_CONFIG调整参数")
    else:
        print(f"   1. 在QMT的tick周期中运行此模块（传统模式）")
        print(f"   2. 需等待足够的tick数据积累")
        print(f"   3. 可设置enable_data_buffer=True启用增强功能")

    print("="*60)

if __name__ == "__main__":
    print(__description__)
    print(f"版本: {__version__} - 数据缓冲区增强版")
    print("✅ 模块加载完成，可在QMT中使用")
    print("\n" + "="*60)
    show_module_status()
    print("")
    show_buffer_usage_guide()
    print("")
    show_atr_config_details()
    show_account_id_config()
    show_kline_merge_config()
    show_order_management_config()
